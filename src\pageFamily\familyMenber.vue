<template>
	<view class="px-[20rpx] py-[28rpx]">
	<view class="flex flex-row items-center bg-white rounded-[16rpx] mt-[8rpx] py-[30rpx] px-[40rpx]" 
	v-for="(item,index) in familyMembers" :key="index" @click="onClickItem(item)">
		<!-- <image src="@/static/products/blood-pressure02.png" class="w-[80rpx] h-[80rpx] object-contain" /> -->
		<uni-icons custom-prefix="iconfont" type="icon-tijianjilu" size="40" color="#014777"></uni-icons>
	
		<view class="flex flex-col flex-1 pl-[20rpx]">
			<text class="text-lg">{{item.personName}}</text>
		</view>
	
		<view class="flex flex-row items-center">
			<uni-icons type="right" size="32" color="#014777"></uni-icons>
		</view>
	</view>
	
	</view>
</template>

<script setup lang='ts'>
	import {reactive,ref,onMounted} from "vue";
	import { onLoad, onShow } from "@dcloudio/uni-app";
	import {
		getUserFamilyMembers,
	} from '@/common/api/family'
	let userInfo=null;
	const familyMembers= ref<{personId:string;personName:string}[]>([]);
	const onClickItem=(item:any)=>{
		console.log("item"+JSON.stringify(item));
		uni.$emit('selectMenberType',item);
		uni.navigateBack();
	}
	const requestUserFamilyMembers=()=>{
		getUserFamilyMembers({userId:userInfo.userId})
		.then(res=>{
			//处理成功相应
			familyMembers.value=res.data
		})
	}
	onMounted(()=>{
		userInfo=uni.getStorageSync('userInfo');
		if(!userInfo||!userInfo.personId || !userInfo.userId){
			uni.showModal({
				content:"用户信息获取失败，请重新登录",
				success: (res) => {
					if(res.confirm){
						uni.reLaunch({
							url:'/pages/login',
						})
					}
				}
			})
			return
		}
		setTimeout(()=>{
			//调取获取家庭成员列表的函数
			requestUserFamilyMembers();
		},300);
	})
</script>

<style>
</style>