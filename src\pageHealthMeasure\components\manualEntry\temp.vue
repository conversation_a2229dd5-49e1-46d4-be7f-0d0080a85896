<template>
	<view class="flex flex-col items-stretch m-[28rpx] px-[20rpx] py-[28rpx]">

		<up-form ref="uFormRef" labelPosition="left" :rules="rules" :model="submitForm">
			<view class="flex items-center bg-[#ffffff] rounded-[16rpx] px-[20rpx] py-[36rpx]">
				<view class="text-lg">体温</view>
				<view class="flex-1 pl-[36rpx]">
					<up-form-item label="" prop="temp" labelWidth="0rpx">
						<view style="width: 100%;" @click="showTempPicker=true">
							<up-input v-model="submitForm.temp" placeholder="输入体温值" border="none" readonly />
						</view> 
					</up-form-item>
				</view>

				<view class="flex flex-row items-center">
					<view class="px-[12rpx] text-[#999]">℃</view>
					<uni-icons type="right" size="32" color="#014777"></uni-icons>
				</view>
			</view>

			<view class="flex items-center bg-[#ffffff] rounded-[16rpx] mt-[36rpx] px-[20rpx] py-[36rpx]">
				<view class="text-lg">时间</view>
				<view class="flex-1 pl-[36rpx]">
					<up-form-item label="" prop="datetime" labelWidth="0rpx">
						<uni-datetime-picker type="datetime" v-model="submitForm.datetime" />
					</up-form-item>
				</view>

				<uni-icons type="right" size="32" color="#014777"></uni-icons>
			</view>
		</up-form>

		<view class="flex flex-1 justify-center text-white bg-[#0052A8] rounded-[16rpx] mt-[50rpx] px-[20rpx] py-[36rpx]"
			@click="doSubmit()">
			确定
		</view>
		
		<numeric-picker :show="showTempPicker" dataType="float1" :range="[35.5,42.5]" :defValue="submitForm.temp" @onCancel="showTempPicker=false"
			@onConfirm="onConfirm"></numeric-picker>
	</view>
</template>

<script setup lang="ts">
	import moment from 'moment';
	import { onLoad, onShow } from "@dcloudio/uni-app";
	import { onMounted, ref, reactive, computed, watch } from 'vue';
	import type { UniFormRef } from '@/uni_modules/uview-plus/types'
	const uFormRef = ref<UniFormRef | null>(null)  

	import { createTempRecord } from '@/common/api/measure'

	const selectMenberType = ref<{ personName : string, personId : string | number }>();

	const props = withDefaults(
		defineProps<{
			viewMode : string;
			dataType : string;
		}>(),
		{
			//没有默认值就是必填项
		}
	);

	interface MySubmitForm {
		temp : string 
		datetime : string 
	}
	const submitForm = ref<MySubmitForm>(
		{
			temp: "36.5", 
			datetime: "" 
		})

	const showTempPicker = ref(false);  
	const onConfirm = (selValue : string) => {
		console.log("onConfirm", selValue);

		if (!selValue) {
			uni.showToast({
				title: "请选择有效的数值",
				icon: "none"
			})
			return
		}
		submitForm.value.temp = selValue;
		showTempPicker.value = false;
	};
 
	const rules = {
		'temp': {
			type: 'string',
			required: true,
			message: '请输入高压值',
			trigger: ['blur'],
		}, 
		'datetime': {
			type: 'string',
			required: true,
			message: '请选择时间',
			trigger: ['blur', 'change'],
		}
	}; 

	const doSubmit = () => {
		console.log("submitForm: " + JSON.stringify(submitForm.value));
		if (!uFormRef.value) {
			return
		}

		uFormRef.value.validate().then((valid : boolean) => {

			console.log(valid);
			if (valid) {
				//请求接口  
				let bodyParams = {
				  "measureTimeStr": moment(submitForm.value.datetime).format("YYYY-MM-DD HH:mm"), 
				  "personId": selectMenberType.value?.personId,
				  "personName": selectMenberType.value?.personName,
				  "temp": submitForm.value.temp
				}
			createTempRecord(bodyParams).then((res) => {
			 uni.showToast({
			 	title: '提交成功',
			 	icon: 'none'
			 })
			 
			 submitForm.value = {
			 		temp: "", 
			 		datetime: ""
			 	};
		}).catch((error) => { 
			uni.showToast({
				title: '提交失败',
				icon: 'none'
			})
		})

			} else {
				uni.showToast({
					title: "请规范完成表单录入",
					icon: "none"
				})
			}
		}).catch((error : any) => {

			console.log("error: " + JSON.stringify(error));
			// 处理验证错误  
			uni.showToast({
				title: "请规范完成表单录入",
				icon: "none"
			})
		});
	}

	watch(
		() => props.viewMode,
		(value) => {
			console.log('props.viewMode', props.viewMode);

		},
		{ deep: false, immediate: true }
	);
	
	onMounted(() => {
		let lastUserInfo = uni.getStorageSync("lastSelectedUserInfo");
		let userInfo = uni.getStorageSync("userInfo");
	
		if (!lastUserInfo || !lastUserInfo.personId) {
			if (!userInfo || !userInfo.personId) {
				uni.showModal({
					content: "用户信息获取失败，请重新登录",
					success: (res) => {
						if (res.confirm) {
							uni.reLaunch({
								url: '/pages/login',
							})
						}
					}
				})
				return
			} else {
				selectMenberType.value = { personId: userInfo.personId, personName: userInfo.personInfo.personName }
			}
		} else {
			selectMenberType.value = { personId: lastUserInfo.personId, personName: lastUserInfo.personName }
		} 
	
	});
</script>

<style lang="scss">
	.u-page__tag-item {
		margin-right: 20px;
	}
</style>