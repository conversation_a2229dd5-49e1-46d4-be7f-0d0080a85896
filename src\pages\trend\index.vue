<template>
	<view class="p-[20rpx]">
		<view class="bg-white rounded-[16rpx] m-[15rpx] p-[40rpx]">
			<view class="flex items-center">
				<text class="text-xl text-[#000]">健康风险评估</text>
				<uni-icons custom-prefix="iconfont" type="icon-zhongyaotishi" color="#FF4032" size="23"></uni-icons>
			</view>
			<view class="text-[#FF4032] flex flex-col py-3">
				<text class="text-base">存在健康风险，建议尽早寻求医生的帮助。</text>
				<view class="flex flex-col">
					<view><text class="bg-[#014777] rounded-2xl w-[18rpx] h-[18rpx] inline-block"></text>&nbsp;&nbsp;<text
							class="text-lg font-bold">体温 39.6 </text><text>属于过高范围。</text></view>
							<view><text class="bg-[#014777] rounded-2xl w-[18rpx] h-[18rpx] inline-block"></text>&nbsp;&nbsp;<text
									class="text-lg font-bold">收缩压 130 mmHg </text><text>建议将其控制在 130 mmHg 以下。</text></view>
					
				</view>
				<text
					class="text-[#999] text-sm pt-3">根据世界卫生组织(WHO)的建议，慢性疾病的风险因素包括吸烟、缺乏运动、不健康饮食和过量饮酒。如果血糖、血压和血脂长期控制不良，可能预示着代谢综合症的风险，需特别注意，您的健康可能存在潜在问题。</text>
			</view>
		</view>
		<view class="flex justify-center p-[20rpx]">
			<uni-segmented-control style="width: 600rpx;" :current="current" :values="items" style-type="button"
				active-color="#014777" @clickItem="onClickItem" />
		</view>
		<view class="flex flex-col justify-items-center" v-if="current === 0">
			<view class="bg-white rounded-[16rpx] m-[15rpx] p-[40rpx]">
				<view class="flex items-center justify-between">
					<text class="text-xl text-[#014777]">血压</text>
					<text class="text-xl text-[#014777]">2024-11-11</text>
				</view>
				<view class="py-[24rpx]">
					<view class="flex flex-row justify-center items-center">
						<text class="text-[#999]">平均血压</text>
						<text class="text-2xl text-[#000] px-[10rpx]">149/87</text>
						<text class="text-[#999]">mmHg</text>
						<text class="px-[10rpx]">|</text>
						<text class="text-[#000] text-yellow-500">轻度</text>
					</view>
					<view class="flex flex-row justify-center items-center">
						<text class="text-[#999]">平均脉搏</text>
						<text class="text-2xl text-[#000] px-[10rpx]">80</text>
						<text class="text-[#999]">次/分钟</text>
					</view>
				</view>

				<view class="charts-box">
					<qiun-data-charts type="area" :opts="bloodPressureOpts" :chartData="bloodPressureChartData" />
				</view>
			</view>
			<view class="bg-white rounded-[16rpx] m-[15rpx] p-[40rpx]">
				<view class="flex flow-row items-center">
					<image src="@/static/treed01.png" class="w-[102rpx] h-[82rpx] pt-[10rpx] pl-[10rpx]" /><text
						class="text-lg text-[#000] pl-[10rpx]">《2024年中国高血压预防指南》血压分级标准</text>
				</view>
			</view>
			<view class="flex flex-col bg-white rounded-[16rpx] m-[15rpx] p-[40rpx]">
				<view class="flex items-center justify-between">
					<text class="text-xl text-[#014777]">血氧</text>
					<text class="text-xl text-[#014777]">2024-11-11</text>
				</view>
				<view class="py-[24rpx]">
					<view class="flex flex-row justify-center items-center">
						<text class="text-[#999]">平均血氧</text>
						<text class="text-2xl text-[#000] px-[10rpx]">99%</text>
					</view>
				</view>

				<view class="charts-box">
					<qiun-data-charts type="column" :opts="oximetryOpts" :chartData="oximetryChartData" />
				</view>
			</view>
			<view class="bg-white rounded-[16rpx] m-[15rpx] p-[20rpx]">
				<view class="flex flow-row items-center" @click="gotoSleepTest">
					<image src="@/static/treed02.png" class="w-[96rpx] h-[96rpx] pt-[10rpx] pl-[10rpx]" /><text
						class="text-lg text-[#000] pl-[10rpx]">睡眠评测</text>
				</view>
			</view>
			<view class="bg-white rounded-[16rpx] m-[15rpx] p-[40rpx]">
				<view class="flex items-center justify-between">
					<text class="text-xl text-[#014777]">体温</text>
					<text class="text-xl text-[#014777]">2024-11-11</text>
				</view>
				<view class="py-[24rpx]">
					<view class="flex flex-col items-center">
						<text class="text-[#999]">14:00-15:00</text>
						<view class="flex flex-row justify-center items-center">
							<text class="text-2xl text-[#000] px-[10rpx]">36.5-39.6</text>
							<text class="text-[#999]">℃</text>
						</view>
					</view>
				</view>

				<view class="charts-box">
					<qiun-data-charts type="area" :opts="temperatureOpts" :chartData="temperatureChartData" />
				</view>
			</view>
			<view class="bg-white rounded-[16rpx] m-[15rpx] p-[40rpx]">
				<view class="flex items-center justify-between">
					<text class="text-xl text-[#000]">血糖</text>
					<text class="text-xl text-[#000]">2024-11-11</text>
				</view>
				<view class="py-[24rpx]">
					<view class="flex flex-col items-center">
						<text class="text-[#999]">14:00-15:00</text>
						<view class="py-[10rpx] flex flex-col items-center">
							<view class="flex flex-row justify-items-center items-center">
								<view><text class="text-[#999]">低值</text><text
										class="text-2xl text-[#000] px-[10rpx]">5.0</text><text
										class="text-[#999]">>mmol/L</text></view>
								<view><text class="text-[#999]">高值</text><text
										class="text-2xl text-[#000] px-[10rpx]">5.5</text><text
										class="text-[#999]">>mmol/L</text></view>
							</view>
							<view class="flex flex-row justify-items-center items-center">
								<view class="px-[20rpx]">
									<text class="text-[#19c3ba]">正常</text>
									<text class="px-[10rpx]">|</text>
									<text class="text-[#999]">午餐前</text>
								</view>
								<view class="px-[20rpx]">
									<text class="text-[#19c3ba]">正常</text>
									<text class="px-[10rpx]">|</text>
									<text class="text-[#999]">午餐后</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<view class="charts-box">
					<qiun-data-charts type="area" :opts="bloodSugarOpts" :chartData="bloodSugarChartData" />
				</view>
			</view>
			<view class="bg-white rounded-[16rpx] m-[15rpx] p-[20rpx]">
				<view class="flex flow-row items-center">
					<image src="@/static/treed03.png" class="w-[110rpx] h-[110rpx] pt-[10rpx] pl-[10rpx]" /><text
						class="text-lg text-[#000] pl-[10rpx]">血糖健康医学诊断方法</text>
				</view>
			</view>
			<view class="bg-white rounded-[16rpx] m-[15rpx] p-[40rpx]">
				<view class="flex items-center justify-between">
					<text class="text-xl text-[#000]">体脂</text>
					<text class="text-xl text-[#000]">2024-11-11</text>
				</view>
				<view class="flex flex-row justify-between py-[10rpx]">
					<view class="flex flex-col">
						<view>
							<view>
								<text class="text-2xl text-[#000] px-[10rpx]">62.0</text>
								<text class="text-[#999]">公斤</text>
							</view>
							<view class="text-[#999]">BMI 24.2 超重</view>
						</view>
					</view>
					<view><uni-icons type="arrow-up" size="23" color="#ffffff"></uni-icons><text
							class="text-xl px-[10rpx]">3.0</text><text class="text-[#999]">较上次一次</text></view>
				</view>
			</view>
		</view>
		<view class="flex flex-col justify-items-center" v-if="current === 1">
			<view class="pt-[36rpx] pb-[12rpx] font-bold">血压异常记录</view>
			<view class="grid grid-cols-3">
				<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[12rpx] py-[20rpx]">
					<text class="font-bold text-[#641013]">重度</text>
					<text class="pt-[12rpx]">{{ pressureInfo.level1 }}次</text>
				</view>
			
				<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[12rpx] py-[20rpx]">
					<text class="font-bold text-[#951d1d]">中度</text>
					<text class="pt-[12rpx]">{{ pressureInfo.level2 }}次</text>
				</view>
			
				<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[12rpx] py-[20rpx]">
					<text class="font-bold text-[#bd3124]">轻度</text>
					<text class="pt-[12rpx]">{{ pressureInfo.level3 }}次</text>
				</view>
			
				<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[12rpx] py-[20rpx]">
					<text class="font-bold text-[#81b337]">正常</text>
					<text class="pt-[12rpx]">{{ pressureInfo.stateNormal }}次</text>
				</view>
			
				<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[12rpx] py-[20rpx]">
					<text class="font-bold text-[#347caf]">偏低</text>
					<text class="pt-[12rpx]">{{ pressureInfo.stateLow }}次</text>
				</view>
			
				<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[12rpx] py-[20rpx]">
					<text class="font-bold text-[#333]">全部</text>
					<text class="pt-[12rpx]">{{ pressureInfo.stateAll }}次</text>
				</view>
			
			
			</view>
			<view class="grid grid-cols-2">
				<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[24rpx] py-[24rpx]" @click="gotoManualEntry(0)">
					<text class="font-bold">手动输入</text>
				</view>
			
				<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[24rpx] py-[24rpx]" @click="gotoMeasure">
					<text class="font-bold">测量血压</text>
				</view>
			</view>
			<view class="flex flex-row items-center bg-white rounded-[16rpx] mt-[8rpx] py-[30rpx] px-[40rpx]" @click="gotoPlan">
				<!-- <image src="@/static/products/blood-pressure02.png" class="w-[80rpx] h-[80rpx] object-contain" /> -->
				<uni-icons customPrefix="iconfont" type="icon-xieya1" size="42" color="#f00a4f"></uni-icons>
			
				<view class="flex flex-col flex-1 pl-[20rpx]">
					<text class="text-lg">测量计划</text>
					<view class="text-sm pt-[8rpx]">
						按预设间隔时间，连续测量血压
					</view>
				</view>
			
				<view class="flex flex-row items-center">
					<uni-icons type="right" size="32" color="#014777"></uni-icons>
				</view>
			</view>
			<view class="pt-[36rpx] pb-[12rpx] font-bold">体温异常记录</view>
			<view class="grid grid-cols-3">
				<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[12rpx] py-[20rpx]">
					<text class="font-bold text-[#347caf]">偏低</text>
					<text class="pt-[12rpx]">{{ tempInfo.stateLow }}次</text>
				</view>
			
				<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[12rpx] py-[20rpx]">
					<text class="font-bold text-[#333]">偏高</text>
					<text class="pt-[12rpx]">{{ tempInfo.stateHigh }}次</text>
				</view>
				<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[12rpx] py-[20rpx]">
					<text class="font-bold text-[#81b337]">正常</text>
					<text class="pt-[12rpx]">{{ tempInfo.stateNormal }}次</text>
				</view>
			</view>
			<view class="grid grid-cols-2">
				<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[24rpx] py-[24rpx]" @click="gotoManualEntry(1)">
					<text class="font-bold">手动输入</text>
				</view>
			
				<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[24rpx] py-[24rpx]" @click="gotoMeasure">
					<text class="font-bold">测量体温</text>
				</view>
			</view>
			<!-- 血糖 -->
			<view class="pt-[36rpx] pb-[12rpx] font-bold">血糖异常记录</view>
			<view class="grid grid-cols-3">
				<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[12rpx] py-[20rpx]">
					<text class="font-bold text-[#347caf]">偏低</text>
					<text class="pt-[12rpx]">{{ sugarInfo.stateLow }}次</text>
				</view>
			
				<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[12rpx] py-[20rpx]">
					<text class="font-bold text-[#333]">偏高</text>
					<text class="pt-[12rpx]">{{ sugarInfo.stateHigh }}次</text>
				</view>
				<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[12rpx] py-[20rpx]">
					<text class="font-bold text-[#81b337]">正常</text>
					<text class="pt-[12rpx]">{{ sugarInfo.stateNormal }}次</text>
				</view>
			</view>
			<view class="grid grid-cols-2">
				<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[24rpx] py-[24rpx]" @click="gotoManualEntry(2)">
					<text class="font-bold">手动输入</text>
				</view>
			
				<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[24rpx] py-[24rpx]" @click="gotoMeasure">
					<text class="font-bold">测量血糖</text>
				</view>
			</view>
			<!-- 体脂 -->
			<view class="pt-[36rpx] pb-[12rpx] font-bold">体脂记录</view>
			<view class="flex flex-col items-stretch">
			
				<view class="bg-white rounded-[16rpx] m-[15rpx] p-[30rpx]">
			
					<view class="charts-box">
						<qiun-data-charts type="area" :opts="chartsOpts" :chartData="chartData" />
					</view>
				</view>
			
				<view class="grid grid-cols-2">
					<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[24rpx] py-[24rpx]" @click="gotoManualEntry(3)">						<text class="font-bold">手动输入</text>
					</view>
			
					<!-- @click="gotoMeasure" -->
					<view class="flex flex-col items-center bg-[#ddd] rounded-[16rpx] m-[24rpx] py-[24rpx]">
						<text class="font-bold">测量体重</text>
					</view>
				</view>
				</view>
		</view>
		
		<view class="flex flex-col justify-items-center" v-if="current === 2">
			<view class="pt-[36rpx] pb-[12rpx] font-bold">血压记录</view>
			
			<view class="flex flex-col mt-[8rpx] rounded-[16rpx] bg-[#fff]">
				<view class="flex flex-row justify-between list-border p-[24rpx]" v-for="(item, index) in pressurerecordList"
					:key="index ">
					<view>
					<text>{{ item.height || "-" }}/</text>
					<text>{{ item.low || "-" }}mmHg</text>
					</view>
					<text>{{ item.date }}</text>
				</view>
			</view>
		<view class="pt-[36rpx] pb-[12rpx] font-bold">血糖记录</view>
		<view class="flex flex-col">
			<view class="flex flex-col mt-[20rpx] rounded-[16rpx] bg-[#fff]" v-for="(item, index) in recordList" key="index ">
				<view class="flex flex-row justify-between list-border p-[24rpx]">
					<text>{{ item.date }}</text>
					<text>单位：mmol/L</text>
				</view>
				<view class="flex flex-col mx-[24rpx] bg-[#333]" style="height: 2.5rpx;"/>
				<!-- justify-between -->
				<view class="flex flex-row justify-around list-border p-[24rpx]">
					<view class="flex flex-col items-center py-[12rpx]">
						<text class="font-bold text-2xl">{{ item.empty || '-' }}</text>
						<text class="pt-[12rpx] text-[#333]">空腹</text>
					</view>
					<view class="flex flex-col items-center py-[12rpx]">
						<text class="font-bold text-2xl">{{ item.after || '-' }}</text>
						<text class="pt-[12rpx] text-[#333]">餐后2小时</text>
					</view>
				</view>
			</view>
		</view>

	</view>
	<view class="pt-[36rpx] pb-[12rpx] font-bold">血体脂记录</view>
	
	<view class="flex flex-col mt-[8rpx] rounded-[16rpx] bg-[#fff]">
		<view class="flex flex-row justify-between list-border p-[24rpx]" v-for="(item, index) in bimrecordList"
			:key="index ">
			<text>{{ item.date }}</text>
			<text>{{ item.weight || "-" }}Kg</text>
			<text>{{ item.bmi || "-" }}%</text>
		</view>
	</view>
		</view>
</template>

<script setup lang="ts">
	import { onMounted, ref } from "vue";
	import { onLoad, onShow } from "@dcloudio/uni-app";
	import bloodPressure from "@/pageChronicDisease/components/bloodPressure.vue";
	import bloodSugar from "@/pageChronicDisease/components/bloodSugar.vue";
	import bmi from "@/pageChronicDisease/components/bmi.vue";
	// 使用ref创建一个响应式引用
	const bloodPressureChart = ref(null);
	const oximetryChart = ref(null);
	const temperatureChart = ref(null);
	const bloodSugarChart = ref(null);
	const bloodPressureChartData = ref();
	const oximetryChartData = ref();
	const temperatureChartData = ref();
	const bloodSugarChartData = ref();
	const chartsOpts = ref<any>({
		color: ['#188cf7', '#93c978'],
		yAxis: {
			/* data: [{ min: 0, max: 300 }],
			splitNumber: 5 */
		},
		padding: [10, 10, 0, 6],
		legend: { show: true },
		extra: {
			area: {
				type: "curve",
				opacity: 0.2,
				addLine: true,
				width: 2,
				gradient: true,
				activeType: "hollow"
			}
		},
	});
	const chartData = ref();
	
	const updateChart = () => {
		console.log("updateMornig: ");
		chartData.value = {
			"categories": ["12-06", "12-07", "12-08", "12:09", "12-10", "12-11"],
			"series": [{
				"name": "体脂率",
				"data": [24, 26, 31, 25, 30, 30]
			}, {
				"name": "体重",
				"data": [51, 56, 50, 54, 56, 58]
			}]
		};
	}
	interface record  {
		level1? : number
		level2? : number
		level3? : number
		stateNormal? : number
		stateLow? : number
		stateAll? : number
		stateHigh?: number
	}
const pressureInfo= ref<record>({
	level1: 0,
	level2: 0,
	level3: 0,
	stateNormal: 0,
	stateLow: 0,
	stateAll: 0
})
const sugarInfo= ref<record>({
				stateNormal: 0,
				stateLow: 0,
				stateHigh: 0
			})
			const tempInfo= ref<record>({
							stateNormal: 0,
							stateLow: 0,
							stateHigh: 0
						})
	interface recordListType  {
				date : string
				empty : string
				after : string
			};
	const	recordList=ref<recordListType[]>([
		{
				date: "2024-12-06",
				empty: "4.0",
				after: "5.0"
			}, {
				date: "2024-12-07",
				empty: "4.5",
				after: "5.0"
			}, {
				date: "2024-12-08",
				empty: "5.0",
				after: "5.5"
			},
			])
	interface	bimList {
				date : string
				weight : string
				bmi : string
			};
	const bimrecordList=ref<bimList[]> ([{
				date: "2024-12-06",
				weight: "52.4",
				bmi: "29"
			},{
				date: "2024-12-07",
				weight: "52.2",
				bmi: "29"
			},{
				date: "2024-12-08",
				weight: "50.9",
				bmi: "28"
			}])
			interface	pressureList {
						date : string
						height : string
						low : string
					};
			const pressurerecordList=ref<pressureList[]> ([{
						date: "2024-12-06 12:00",
						height: "52.4",
						low: "29"
					},{
						date: "2024-12-07 12:30",
						height: "52.2",
						low: "29"
					},{
						date: "2024-12-08 12:48",
						height: "50.9",
						low: "28"
					}])
	// 定义option为响应式数据
	const bloodPressureOpts = ref<any>({
		color: ['#19c3ba', "#fdae61"],
		yAxis: {
			data: [{ min: 40, max: 200 }],
			splitNumber: 8
		},
		padding: [10, 10, 0, 6],
		legend: { show: true },
		extra: {
			area: {
				type: "curve",
				opacity: 0.2,
				addLine: true,
				width: 2,
				gradient: true,
				activeType: "hollow"
			}
		},
	});
	const oximetryOpts = ref<any>({
		color: ["#ff6464", "#fdae61", "#8ad688"],
		yAxis: {
			data: [{ min: 0, max: 100 }],
			splitNumber: 5
		},
		dataLabel: false,
		extra: {
			column: {
				type: "stack",
				width: 8,
				activeBgColor: "#000000",
				activeBgOpacity: 0.08,
				barBorderCircle: true,
				barBorderRadius: [6, 6, 0, 0]
			}
		},
	});
	const temperatureOpts = ref<any>({
		color: ['#19c3ba'],
		yAxis: {
			data: [{ min: 35, max: 42 }],
			splitNumber: 7
		},
		padding: [10, 10, 0, 6],
		legend: { show: false },
		extra: {
			area: {
				type: "curve",
				opacity: 0.2,
				addLine: true,
				width: 2,
				gradient: true,
				activeType: "hollow"
			}
		},
	});
	const bloodSugarOpts = ref<any>({
		color: ['#19c3ba'],
		xAxis: {
			disableGrid: true,
			boundaryGap: "justify"
		},
		yAxis: {
			gridType: "dash",
			dashLength: 2,
			data: [{ min: 0 }],
		},
		padding: [18, 18, 0, 10],
		enableScroll: false,
		legend: { show: false },

		extra: {
			area: {
				type: "curve",
				opacity: 0.2,
				addLine: true,
				width: 2,
				gradient: true,
				activeType: "hollow"
			}
		},
	});

	const items = ["日", "周", "月"];
	const current = ref(0);
	const onClickItem = (e : any) => {
		if (current.value !== e.currentIndex) {
			current.value = e.currentIndex;
		}
	}

	const goLogin = () => {
		uni.navigateTo({
			url: '/pages/login',
		})
	}

	const goHome = () => {
		uni.navigateTo({
			url: '/page_index/index/index',
		})
	}
	const gotoManualEntry = (index :number) => {
		// 血压=0； 体温=1； 血糖=2；体脂=3
		console.log("gotoManualEntry: ",index);
		uni.navigateTo({
			url: "/pageHealthMeasure/manualEntry" + "?params=" + index
		})
	}

	const gotoSleepTest = () => {
		uni.navigateTo({
			url: "/pageSheep/resultList"
		})
	}

	const gotoMeasure = () => {
		console.log("gotoMeasure: ");

	}
	const gotoPlan = () => {
		console.log("gotoPlan: ");
		uni.navigateTo({
			url: '/pageChronicDisease/bloodPressureTestPlan'
		})
	}
	onLoad(() => {
		console.log('TREND【onLoad】：页面加载完成')
	})

	onShow(() => {
		console.log("TREND【onShow】：页面重新可见");

		//检查token情况

	});

	// 使用onMounted生命周期钩子
	onMounted(() => {
		// 组件能被调用必须是组件的节点已经被渲染到页面上
		setTimeout(() => {
			bloodPressureChartData.value = {
				"categories": ["0:00", "4:00", "8:00", "12:00", "16:00", "20:00"],
				"series": [{
					"name": "高压（收缩压）",
					"data": [124, 126, 131, 125, 130, 130]
				}, {
					"name": "低压（舒张压）",
					"data": [81, 76, 80, 74, 76, 78]
				}]
			};

			let oriData = [66, 75, 91, 73, 83, 88];
			let result : { low : number[], middle : number[], good : number[] } = { low: [], middle: [], good: [] }
			for (var i = 0; i < oriData.length; i++) {
				if (oriData[i] >= 90) {
					result.low.push(0)
					result.middle.push(0)
					result.good.push(oriData[i])
				} else if (oriData[i] >= 71) {
					result.low.push(0)
					result.middle.push(oriData[i])
					result.good.push(0)
				} else {
					result.low.push(oriData[i])
					result.middle.push(0)
					result.good.push(0)
				}
			}
			oximetryChartData.value = {
				"categories": ["0:00", "4:00", "8:00", "12:00", "16:00", "20:00"],
				"series": [{
					"name": "≤70%",
					"data": result.low
				}, {
					"name": "71-89%",
					"data": result.middle
				}, {
					"name": "≥90%",
					"data": result.good
				}]
			};

			temperatureChartData.value = {
				"categories": ["0:00", "4:00", "8:00", "12:00", "16:00", "20:00"],
				"series": [{
					"name": "",
					"data": [36.9, 36.9, 37.5, 38.6, 36.5, 38.6]
				}]
			};

			bloodSugarChartData.value = {
				"categories": ["0:00", "4:00", "8:00", "12:00", "16:00", "20:00"],
				"series": [{
					"name": "",
					"data": [35, 36, 31, 33, 13, 34]
				}]
			};
			updateChart();
		}, 300)
	});
</script>

<style scoped lang="scss">
	.wrap {
		padding: 12px;
	}

	.demo-layout {
		height: 25px;
		border-radius: 4px;
	}

	.bg-purple {
		background: #CED7E1;
	}

	.bg-purple-light {
		background: #e5e9f2;
	}

	.bg-purple-dark {
		background: #99a9bf;
	}

	.charts-box {
		width: 100%;
		height: 500rpx;
	}
</style>