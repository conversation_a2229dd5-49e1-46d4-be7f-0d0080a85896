<template>
	<view class="p-[30rpx]">
		<uni-forms ref="customForm" :modelValue="formData" :rules="customRules" labelWidth="100%" label-position="top">

			<uni-forms-item required v-for="(item, index) in questions" :key="index"
				:prop="'dynamicItems[' + index + '].value'">
				<!-- 自定义label插槽 -->
				<template #label>
					<view class="text-[#999] text-sm mb-2">{{ item.questionNo }}</view>
					<text class="text-[#000] text-base mb-2">{{ item.question }}</text>
				</template>

				<template #default>
					<view class="bg-white rounded-[20rpx] p-[30rpx] mt-[16rpx] mb-[30rpx]">
						<radio-group @change="(event:any)=>radioGroupChange(Number(event.detail.value), index)">
							<view v-for="(optionItem, optionIndex) in item.option" :key="optionIndex">
								<view class="flex flex-row justify-between items-center py-[10rpx]">
									<div class="text-base">
										{{ optionItem.anser }}
									</div>
									<radio :value="optionItem.score"></radio>
								</view>
								<up-line v-if="optionIndex < (item.option.length - 1)" color="#999" margin="20rpx 0"></up-line>
							</view>
						</radio-group>
					</view>
				</template>
			</uni-forms-item>

		</uni-forms>

		<button class="my-2" type="primary" style="background-color: #2B97E5; color: white;"
			:style="{opacity:questionAnserFlafs.length < questions.length ? 0.3 : 1}"
			:disabled="questionAnserFlafs.length < questions.length" @click="onClickSubmit">提交</button>
	</view>
</template>

<script setup lang="ts">
	import { ref, onMounted } from 'vue';
	import { onLoad, onShow, onUnload } from "@dcloudio/uni-app";
	import moment from 'moment';

	import { createSleepAssessment } from '@/common/api/sleep';
	import { getQuestions } from "@/model_static/sleepQuestion";

	// 校验表单数据
	const formData = ref({
		sheeptimes: '23:00',
		sleepInto: '',
		sheepDur: '',
		getuptimes: '08:00',
		sleepIntoFrequery: '',
		wakeFrequery: '',
		wcFrequery: '',
		nointoSleep: '',
		breatheFrequery: '',
		snoreFrequery: '',
		coldFrequery: '',
		hotFrequery: '',
		nightmareFrequery: '',
		painFrequery: '',
		otherFrequery: '',
		quantitySheep: '',
		quantityEnergy: '',
		pillFrequery: '',
		tiredFrequery: '',
	});

	let userInfo : any = null;
	const questions = ref<any[]>([]);
	const questionAnserFlafs = ref<number[]>([]);
	const questionScoreList = ref<number[]>([]);

	const radioGroupChange = (score : number, index : number) => {
		console.log("radioGroupChange e: " + score);
		console.log("radioGroupChange", index);
		questionScoreList.value.splice(index, 1, score)
		if (!questionAnserFlafs.value.includes(index)) {
			questionAnserFlafs.value.push(index);
		}

		console.log("questionScoreList.value: " + JSON.stringify(questionScoreList.value));
	};

	// 自定义表单校验规则
	const customRules = {
		sheeptimes: {
			rules: [{
				required: true,
				errorMessage: '姓名不能为空'
			}]
		}
	};

	const customForm = ref();
	const onClickSubmit = () => {
		let totalScore = questionScoreList.value.reduce((accumulator, currentValue) => accumulator + currentValue, 0)
		let content = "睡眠质量很好";
		let sleepLevel = 1;
		if (totalScore > 15) {
			sleepLevel = 4;
			content = "睡眠质量很差";
		} else if (totalScore > 10) {
			sleepLevel = 3;
			content = "睡眠质量一般";
		} else if (totalScore >= 5) {
			sleepLevel = 2;
			content = "睡眠质量还行";
		}

		let assessTime = moment().format('YYYY-MM-DD HH:mm:ss');
		let bodyParams = {
			"assessTime": assessTime,
			"content": content,
			"personId": userInfo.personId,
			"personName": userInfo.personInfo.personName || '',
			"score": totalScore,
			"sleepLevel": sleepLevel
		}

		uni.showLoading({
			title: '正在提交...',
			mask: true
		});
		createSleepAssessment(bodyParams).then((res) => {
			console.log('createSleepAssessment********', res);
            if(res?.success) {
                uni.showToast({
                    title: '提交成功',
                    icon: 'none'
                })
            }

			let encodeParams = encodeURIComponent(JSON.stringify({
				time: assessTime, 
				score: totalScore,
				levelName: content
			}))

			uni.redirectTo({
				url: '/pageSheep/sheepTestResult' + "?params=" + encodeParams,
				animationType: "slide-in-right",
				animationDuration: 300,
			})
            onRefreshPrevPageData()
		}).catch((error) => {
			console.log('requestDataWithDate!!!');
			uni.showToast({
				title: '提交失败',
				icon: 'none'
			})
		}).finally(() => {
			uni.hideLoading()
		})

	};
    
    const onRefreshPrevPageData = () => {
        let pages = getCurrentPages();
        let prevPage = pages[pages.length - 2]; // 上一个页面
        // 直接调用上一个页面的setData()方法，把数据存到上一个页面中去
        prevPage.setData({
            isRefresh: 1 
        })
    }

	onLoad(() => {
		//{"type":"1"}
		console.log('sheepTest【onLoad】：页面加载完成');
		questions.value = getQuestions;

		for (var i = 0; i < questions.value.length; i++) {
			questionScoreList.value.push(0)
		}
	})

	onMounted(() => {
		userInfo = uni.getStorageSync("userInfo");
		if (!userInfo || !userInfo.personId || !userInfo.userId) {
			uni.showModal({
				content: "用户信息获取失败，请重新登录",
				success: (res) => {
					if (res.confirm) {
						uni.reLaunch({
							url: '/pages/login',
						})
					}
				}
			})
			return
		}

	});

	onShow(() => {
		console.log("sheepTest【onShow】：页面重新可见");

		//检查token情况

	});

	onUnload(() => {
		console.error('页面onUnload');
	})
</script>

<style scoped lang="scss">

</style>