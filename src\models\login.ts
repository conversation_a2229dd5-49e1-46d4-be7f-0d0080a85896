/*
 * @Description:
 * @Author: h<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-11-29 
 * @LastEditTime: 2024-11-29
 * @LastEditors: huangyanqiu
 */
export interface ILogin {
    phoneNumber: string
}

export interface ILoginParams {
  page: number
  pageSize: number
  [keys: string]: any
}

export interface ILoginResp {
  userId: string
  personId: string
  nickname?: string
  token: string,
  personInfo: Object
}

export interface UserInfosStates {
  userInfos: ILoginResp
  token: string
}
