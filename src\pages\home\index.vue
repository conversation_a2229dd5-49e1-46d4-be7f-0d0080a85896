<template>
	<view>
		<view class="bg-[#237FC2] h-[550rpx]" style="padding-top: 0rpx;">
			<view class="p-[30rpx] pt-[20rpx] flex flex-row justify-between">
				<view class="flex flex-row" @click="selectedChronicDisease">
					<view class="text-2xl text-[#00FFC5]">{{ selectSignType ? selectSignType.name : '请选择' }}</view>
					<image src="@/static/switch.png" class="w-[46rpx] h-[46rpx] pt-[10rpx] pl-[10rpx]" />
				</view>
				<view class="flex flex-row" @click='selectedMenber'>
					<view class="text-[36rpx] text-white">{{selectMenberType ? selectMenberType.personName:'本人' }}</view>
					<uni-icons type="list" size="23" color="#ffffff"></uni-icons>
				</view>
			</view>
			<view v-if="mainType" class="mx-[30rpx]">
				<view class="w-full h-[280rpx] rounded-[25rpx] bg-[#ffffff]">
					<view class="flex justify-between p-[30rpx]">
						<view class="flex flex-row">
							<uni-icons custom-prefix="iconfont" :type="mainType.icon" size="48" color="#014777"></uni-icons>
							<view class="ml-[20rpx]">
								<view class="text-[46rpx] text-[#014777]">{{ mainType.typeName}}</view>
								<text class="text-sm text-[#333333]">{{ mainType.measureTime }}</text>
							</view>
						</view>
						<!-- 右 设备连接状态-->
						<!-- <view>
							<uni-icons custom-prefix="iconfont" type="icon-dianchi" size="26" color="#014777" class="relative">
								<text class="text-base align-top">20%</text>
							</uni-icons>
							<uni-icons custom-prefix="iconfont" type="icon-wifi1" size="26" color="#51DA4E"></uni-icons>
						</view> -->
					</view>
					<view class="flex items-center px-[30rpx]">
						<view>
							<!-- <text class="text-4xl text-[#FF4032]">135</text><text class="text-3xl mx-[20rpx]">/</text> -->
							<text class="text-4xl">{{mainType.measureValue}}</text>
							<text class="text-sm">{{mainType.measureValueUnit}}</text>
						</view>
						<view v-if="mainType.type == 1">
							<text class="text-4xl">{{mainType.pulse}}</text>
							<text class="text-sm">bpm</text>
						</view>
					</view>
				</view>
			</view>
			<view v-else class="mx-[30rpx]">
				<view class="w-full h-[280rpx] rounded-[25rpx] bg-[#ffffff]">
					<view class="flex items-center p-[30rpx]">
						<view class="flex flex-row" @click="selectedChronicDisease">
							<uni-icons custom-prefix="iconfont" type="" size="48" color="#014777"></uni-icons>
							<view class="ml-[20rpx]">
								<!-- <uni-icons type="arrow-up" color="#FF4032" size="23"></uni-icons> -->
								<view class="flex items-center p-3">
									<uni-icons custom-prefix="iconfont" type="icon-zanwushuju" size="62" color="#999"></uni-icons>
									<text class="pl-[20rpx] text-[#333]">暂无关注数据，请选择</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<uni-card>
				<view class="w-full rounded-[25rpx] bg-[#ffffff]">
					<view class="flex flex-row justify-between">
						<view class="text-[#014777] text-[36rpx]">今日用药提醒</view>
						<view class="text-[#014777]" @click="addMedicine">去添加<uni-icons type="right" size="16"
								color="#014777"></uni-icons>
						</view>
					</view>
					<view class="py-3 truncate" style="overflow: auto;">
						<text v-for="(item,index) in tipList" :key="index"><text class="text-[#333333]">{{item.name}}</text><text
								class="text-[#FF4032] text-lg">&nbsp;{{item.time}}&nbsp;</text><text
								class="text-[#333333]">后服用&nbsp;&nbsp;&nbsp;&nbsp;</text></text>
					</view>
				</view>
			</uni-card>
			<view class="m-[30rpx]">
				<text class="text-2xl">健康监测数据</text>
			</view>
			<view class="mx-[15rpx]">
				<healthObsCard :data="measureData" @cardClick="healthObsItemClick" />
			</view>
			<!-- <view class="py-[30rpx] px-[120rpx]">
				<u-button type="primary" :disabled="disabled" :plain="true" text="编辑数据模块"></u-button>
			</view> -->
			<view class="m-[30rpx]">
				<text class="text-2xl">其他数据</text>
			</view>
			<view class="px-[20rpx]">
				<view class="grid grid-cols-2">
					<view class="flex items-center bg-white rounded-[16rpx] m-[15rpx] py-[35rpx] px-[20rpx]"
						@click="handleBaseInfo">
						<uni-icons custom-prefix="iconfont" type="icon-jiankangdangan" size="32" color="#e691aa"></uni-icons>
						<text class="pl-[20rpx]">个人档案</text>
					</view>
					<view class="flex items-center bg-white rounded-[16rpx] m-[15rpx] py-[35rpx] px-[20rpx]"
						@click="handleUploadInfo">
						<uni-icons custom-prefix="iconfont" type="icon-tijianbaogao" size="30" color="#23cfae"></uni-icons>
						<text class="pl-[20rpx]">上传报告</text>
					</view>
					<view class="flex items-center bg-white rounded-[16rpx] m-[15rpx] py-[35rpx] px-[20rpx]" @click="handleSheel">
						<uni-icons custom-prefix="iconfont" type="icon-shuimian" size="32" color="#7e71d1"></uni-icons>
						<text class="pl-[20rpx]">睡眠评测</text>
					</view>
					<view class="flex items-center bg-white rounded-[16rpx] m-[15rpx] py-[35rpx] px-[20rpx]">
						<uni-icons custom-prefix="iconfont" type="icon-tijianxinxi" size="32" color="#ff6a08"></uni-icons>
						<text class="pl-[20rpx]">年体检指标</text>
					</view>
					<view class="flex items-center bg-white rounded-[16rpx] m-[15rpx] py-[35rpx] px-[20rpx]"
						@click="goChronicDiseasePage">
						<uni-icons custom-prefix="iconfont" type="icon-menzhenmanxingbing" size="34" color="#ff8d2c"></uni-icons>
						<text class="pl-[20rpx]">慢性病指标</text>
					</view>
					<view class="flex items-center bg-white rounded-[16rpx] m-[15rpx] py-[35rpx] px-[20rpx]"
						@click="goMedicineChestPage">
						<uni-icons custom-prefix="iconfont" type="icon-yizhufayao" size="32" color="#42c6ff"></uni-icons>
						<text class="pl-[20rpx]">智慧药箱</text>
					</view>
				</view>
			</view>
            <view class="px-[35rpx]">
                <PreDevice ></PreDevice>
            </view>   
			<view class="h-[140rpx]"></view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { reactive, ref, onMounted } from "vue";
	import { onLoad, onShow, onUnload } from "@dcloudio/uni-app";
	import {
		getLatsRecordByType
	} from '@/common/api/measure'

	import type { IHealthObsResp, PersonSignRecord } from '@/models/healthMeasure'
	import healthObsCard from "./components/healthObsCard.vue";
    import PreDevice from '@/components/PreDevice.vue';
	import clinicIntroductionIcon from "@/static/clinic-introduction.png";
	import companyIntroduction from "@/static/company-introduction.png"
	import rouetIcon from "@/static/route.png";
	import doctorIcon from "@/static/doctor.png";
	import moment from 'moment';
 
	let userInfo : any = null;
	const selectSignType = ref<{ name : string, signType : string | number }>();
	const selectMenberType = ref<{ personName : string, personId : string | number }>();
	const mainType = ref<IHealthObsResp>();

	const disabled = ref(true);

	const tipList = [
		{
			name: "胃炎宁",
			time: '12:30'
		},
		{
			name: "胃炎宁",
			time: '12:30'
		},
		{
			name: "降压药",
			time: '18:00'
		},
	];
	const current = ref(0);

	//const measureData = ref<IHealthObsResp[]>([])
	const measureData = ref<IHealthObsResp[]>([]);
	const handleBaseInfo = () => {
		uni.navigateTo({
			url: '/pageInfo/baseInfo',
			animationType: "slide-in-right",
			animationDuration: 300,

		})
	}
	const selectedMenber = () => {
		uni.navigateTo({
			url: '/pageFamily/familyMenber',
			animationType: "slide-in-right",
			animationDuration: 300,

		})
	}
	const addMedicine = () => {
		uni.navigateTo({
			url: '/pageMedicineChest/addMedicine',
			animationType: "slide-in-right",
			animationDuration: 300,

		})
	}
	const selectedChronicDisease = () => {
		uni.navigateTo({
			url: '/pageFamily/chronicDisease',
			animationType: "slide-in-right",
			animationDuration: 300,

		})
	}
	const handleUploadInfo = () => {
		console.log("进入完善信息方法");
		uni.navigateTo({
			url: '/pageInfo/uploadInfo',
			animationType: "slide-in-right",
			animationDuration: 300,

		})
	}
	const handleSheel = () => {
		uni.navigateTo({
			url: '/pageSheep/resultList',
			animationType: "slide-in-right",
			animationDuration: 300,
		})
	}

	const requestLatsRecordByType = () => {
		let queryDate = moment().format('YYYY-MM-DD');
		//20241213
		let bodyParams = {
			"endTime": queryDate,
			"personId": selectMenberType.value?.personId,
			"signType": 0,//传0
			"startTime": queryDate
		} 
		
		getLatsRecordByType(bodyParams).then((res) => {
			console.log('getLatsRecordByType********', res);

			convertData(res.data);
		}).catch((error) => {
			console.log(error);
			uni.showToast({
				title: '数据拉取失败',
				icon: 'none'
			})
			 
		})
	};

	const convertData = (data : PersonSignRecord) => {
		let result : IHealthObsResp[] = [];
		if (data.bpList && data.bpList.length > 0) {
			let bpListItem = {
				type: "1",
				id: data.bpList[0].recordId,
				typeName: "血压",
				icon: "icon-xieya",
				measureValue: data.bpList[0].sbp + "/" + data.bpList[0].dbp,
				measureValueUnit: "mmol/L",
				pulse: data.bpList[0].pulse,
				hr: "",//心率，血氧专用
				timeFrame: "",//测量时段，血糖专用
				timeFrameName: "",//测量时段名称，血糖专用
				height: "",//身高，体脂专用
				weight: "",//体重，体脂专用
				level: data.bpList[0].bpLevel,
				source: data.bpList[0].source,
				personId: data.bpList[0].personId,
				personName: data.bpList[0].personName,
				measureTime: moment(data.bpList[0].measureTime).format('MM/DD'),
				deviceNo: data.bpList[0].deviceNo,
				others: data.bpList[0].others
				//require: ['餐前'],
			};
			if (selectSignType.value && selectSignType.value.signType == 1) {
				mainType.value = bpListItem
			} else {
				result.push(bpListItem)
			}

		} else if (data.bpList) {
			console.log("~~~~~~~~~~~~");
			//if (selectSignType.value && selectSignType.value.signType != 1) {
				console.log('进入初值~~~')
				result.push({
					type: "1",
					id: "",
					typeName: "血压",
					icon: "icon-xieya",
					measureValue: "-/-",
					measureValueUnit: "mmol/L",
					pulse: "",
					hr: "",//心率，血氧专用
					timeFrame: "",//测量时段，血糖专用
					timeFrameName: "",//测量时段名称，血糖专用
					height: "",//身高，体脂专用
					weight: "",//体重，体脂专用
					level: "",
					source: "",
					personId: "",
					personName: "",
					measureTime: moment().format('MM/DD'),
					deviceNo: "",
					others: ""
					//require: ['餐前'],
				})
			//}
		}

		if (data.spozList && data.spozList.length > 0) {
			let spozListItem = {
				id: data.spozList[0].recordId,
				level: data.spozList[0].spozLevel,
				source: data.spozList[0].source,
				personId: data.spozList[0].personId,
				personName: data.spozList[0].personName,
				measureTime:moment(data.spozList[0].measureTime).format('MM/DD')  ,
				type: "2",
				typeName: "血氧",
				icon: "icon-xieyang",
				measureValue: data.spozList[0].spoz,
				measureValueUnit: "%SpO2",
				pulse: "",
				hr: data.spozList[0].hr,//心率，血氧专用
				timeFrame: "",//测量时段，血糖专用
				timeFrameName: "",//测量时段名称，血糖专用
				height: "",//身高，体脂专用
				weight: "",//体重，体脂专用
				deviceNo: data.spozList[0].deviceNo,
				others: data.spozList[0].others
				//require: ['餐前'],

			};
			if (selectSignType.value && selectSignType.value.signType == 2) {
				mainType.value = spozListItem
			} else {
				result.push(spozListItem)
			}
		} else if (data.spozList) {
			//if (selectSignType.value && selectSignType.value.signType != 2) {
				result.push({
					type: "2",
					id: "",
					typeName: "血氧",
					icon: "icon-xieyang",
					measureValue: "-",
					measureValueUnit: "%SpO2",
					pulse: "",
					hr: "",//心率，血氧专用
					timeFrame: "",//测量时段，血糖专用
					timeFrameName: "",//测量时段名称，血糖专用
					height: "",//身高，体脂专用
					weight: "",//体重，体脂专用
					level: "",
					source: "",
					personId: "",
					personName: "",
					measureTime: moment().format('MM/DD'),
					deviceNo: "",
					others: ""
					//require: ['餐前'],
				})
			//}
		}

		if (data.tempList && data.tempList.length > 0) {
			let tempListItem = {
				id: data.tempList[0].recordId,
				level: data.tempList[0].tempLevel,
				source: data.tempList[0].source,
				personId: data.tempList[0].personId,
				personName: data.tempList[0].personName,
				measureTime:moment( data.tempList[0].measureTime).format('MM/DD'),
				type: "3",
				typeName: "体温",
				icon: "icon-tiwen",
				measureValue: data.tempList[0].temp,
				measureValueUnit: "℃",
				pulse: "",
				hr: "",//心率，血氧专用
				timeFrame: "",//测量时段，血糖专用
				timeFrameName: "",//测量时段名称，血糖专用
				height: "",//身高，体脂专用
				weight: "",//体重，体脂专用
				deviceNo: data.tempList[0].deviceNo,
				others: data.tempList[0].others
				//require: ['餐前'],
			};
			if (selectSignType.value && selectSignType.value.signType == 3) {
				mainType.value = tempListItem
			} else {
				result.push(tempListItem)
			}

		} else if (data.tempList) {
			//if (selectSignType.value && selectSignType.value.signType != 3) {
				result.push({
					type: "3",
					id: "",
					typeName: "体温",
					icon: "icon-tiwen",
					measureValue: "-",
					measureValueUnit: "℃",
					pulse: "",
					hr: "",//心率，血氧专用
					timeFrame: "",//测量时段，血糖专用
					timeFrameName: "",//测量时段名称，血糖专用
					height: "",//身高，体脂专用
					weight: "",//体重，体脂专用
					level: "",
					source: "",
					personId: "",
					personName: "",
					measureTime: moment().format('MM/DD'),
					deviceNo: "",
					others: ""
					//require: ['餐前'],
				})
			//}

		}

		if (data.bgList && data.bgList.length > 0) {
			let bgListItem = {
				id: data.bgList[0].recordId,
				level: data.bgList[0].bgLevel,
				source: data.bgList[0].source,
				personId: data.bgList[0].personId,
				personName: data.bgList[0].personName,
				measureTime:moment( data.bgList[0].measureTime).format('MM/DD'),
				type: "4",
				typeName: "血糖",
				icon: "icon-xietang",
				measureValue: data.bgList[0].bg,
				measureValueUnit: "mmol/L",
				deviceNo: data.bgList[0].deviceNo,
				others: data.bgList[0].others,
				//require: ['餐前'], 
				timeFrame: data.bgList[0].timeFrame,
				timeFrameName: data.bgList[0].timeFrameName,
				pulse: "",
				hr: "",//心率，血氧专用
				height: "",//身高，体脂专用
				weight: "",//体重，体脂专用
			};
			if (selectSignType.value && selectSignType.value.signType == 4) {
				mainType.value = bgListItem
			} else {
				result.push(bgListItem)
			}

		} else if (data.bgList) {
			//if (selectSignType.value && selectSignType.value.signType != 4) {
				result.push({
					type: "4",
					id: "",
					typeName: "血糖",
					icon: "icon-xietang",
					measureValue: "-",
					measureValueUnit: "mmol/L",
					pulse: "",
					hr: "",//心率，血氧专用
					timeFrame: "",//测量时段，血糖专用
					timeFrameName: "",//测量时段名称，血糖专用
					height: "",//身高，体脂专用
					weight: "",//体重，体脂专用
					level: "",
					source: "",
					personId: "",
					personName: "",
					measureTime: moment().format('MM/DD'),
					deviceNo: "",
					others: ""
					//require: ['餐前'],
				})
			//}
		}

		if (data.bmiList && data.bmiList.length > 0) {
			let bmiListItem = {
				id: data.bmiList[0].recordId,
				level: data.bmiList[0].bmiLevel,
				source: data.bmiList[0].source,
				personId: data.bmiList[0].personId,
				personName: data.bmiList[0].personName,
				measureTime:moment( data.bmiList[0].measureTime).format('MM/DD'),
				type: "5",
				typeName: "体脂",
				icon: "icon-tizhi",
				measureValue: data.bmiList[0].bmi,
				measureValueUnit: "%",
				deviceNo: data.bmiList[0].deviceNo,
				others: data.bmiList[0].others,
				//require: ['餐前'], 
				timeFrame: "",
				timeFrameName: "",
				pulse: "",
				hr: "",//心率，血氧专用
				height: data.bmiList[0].height,//身高，体脂专用
				weight: data.bmiList[0].weight,//体重，体脂专用
			};
			if (selectSignType.value && selectSignType.value.signType == 5) {
				mainType.value = bmiListItem
			} else {
				result.push(bmiListItem)
			}
		} else if (data.bmiList) {
			//if (selectSignType.value && selectSignType.value.signType != 5) {
				result.push({
					type: "5",
					id: "",
					typeName: "体脂",
					icon: "icon-tizhi",
					measureValue: "-",
					measureValueUnit: "%",
					pulse: "",
					hr: "",//心率，血氧专用
					timeFrame: "",//测量时段，血糖专用
					timeFrameName: "",//测量时段名称，血糖专用
					height: "",//身高，体脂专用
					weight: "",//体重，体脂专用
					level: "",
					source: "",
					personId: "",
					personName: "",
					measureTime: moment().format('MM/DD'),
					deviceNo: "",
					others: ""
					//require: ['餐前'],
				})
			//}
		}

		console.log("mainType: " + JSON.stringify(mainType.value));
		measureData.value = result;
	}


	const healthObsItemClick = (item : IHealthObsResp) => {
		uni.navigateTo({
			url: "/pageHealthMeasure/historyIndex" + "?params=" + item.type,
			animationType: "slide-in-right",
			animationDuration: 300,
		});
	};

	const goChronicDiseasePage = () => {
		//使用switchTab方法跳转到一个定义在tabBar中的页面
		uni.navigateTo({
			url: "/pageChronicDisease/target"
		})
	};

	const goMedicineChestPage = () => {
		//使用switchTab方法跳转到一个定义在tabBar中的页面
		uni.switchTab({
			url: "/pages/medicineChest/index"
		})
	}

	onLoad(() => {
		console.log('HOME【onLoad】：页面加载完成')

		uni.$on("selectSignType", (event) => {
			if (!event) {
				return
			}
			console.log("selectSignType CALLBACK", event);

			selectSignType.value = event;
			requestLatsRecordByType();
		})

		uni.$on("selectMenberType", (event) => {
			if (!event) {
				return
			}
			console.log('selectMenberType CALLBACK', event);

			uni.setStorageSync("lastSelectedUserInfo", event);
			selectMenberType.value = event;
			requestLatsRecordByType();
		});
	})

	onMounted(() => {


	});
	onShow(() => {
		console.log('HOME【onShow】：页面加载完成')
		let lastUserInfo = uni.getStorageSync("lastSelectedUserInfo");
		userInfo = uni.getStorageSync("userInfo");
		
		if (!lastUserInfo || !lastUserInfo.personId) {
			if (!userInfo || !userInfo.personId) {
				uni.showModal({
					content: "用户信息获取失败，请重新登录",
					success: (res) => {
						if (res.confirm) {
							uni.reLaunch({
								url: '/pages/login',
							})
						}
					}
				})
				return
			} else {
				selectMenberType.value = { personId: userInfo.personId, personName: userInfo.personInfo.personName }
			}
		} else {
			selectMenberType.value = { personId: lastUserInfo.personId, personName: lastUserInfo.personName }
		}
		requestLatsRecordByType();
	});

	onUnload(() => {
		console.error('页面onUnload');
		uni.$off("selectSignType");
		uni.$off("selectMenberType");
	})
</script>

<style scoped lang="scss">
	.icon-content {
		display: flex;
		flex-wrap: wrap;
		flex-direction: row;
		justify-content: center;

		.icon-item {
			/* #ifndef APP-NVUE */
			display: flex;
			box-sizing: border-box;
			width: calc(100% / 4);
			/* #endif */
			/* #ifdef APP-NVUE */
			width: 187rpx;
			/* #endif */
			align-items: center;
			padding: 10px;
			text-align: center;
			flex-direction: column;
		}
	}

	::v-deep .uni-card {
		border-radius: 10px;
	}

	::v-deep .segmented-control {
		height: 80rpx;
	}

	::v-deep .segmented-control__item--button {
		border-radius: 50rpx;
	}

	::v-deep .segmented-control__item--button.segmented-control__item--button--first {
		border-top-left-radius: 50rpx;
		border-bottom-left-radius: 50rpx;
	}

	::v-deep .segmented-control__item--button.segmented-control__item--button--last {
		border-top-right-radius: 50rpx;
		border-bottom-right-radius: 50rpx;
	}
</style>