{
	//启动模式配置，仅开发期间生效，用于模拟直达页面的场景
	/* "condition": { //模式配置，仅开发期间生效
		"current": 0, //当前激活的模式（list 的索引项）
		"list": [{
				"name": "报告", //模式名称
				"path": "pageInfo/uploadInfo", //启动页面，必选
				"query": "" //启动参数，在页面的onLoad函数里面得到。
			}, {
				"name": "手动输入", //模式名称
				"path": "pageHealthMeasure/manualEntry", //启动页面，必选
				"query": "" //启动参数，在页面的onLoad函数里面得到。
			}, {
				"name": "历史数据", //模式名称
				"path": "pageHealthMeasure/historyIndex", //启动页面，必选
				"query": "" //启动参数，在页面的onLoad函数里面得到。
			},
			{
				"name": "添加药品",
				"path": "pageMedicineChest/addMedicine"
			},
			{
				"name": "智慧药箱",
				"path": "pages/medicineChest/index"
			},
			{
				"name": "健康趋势",
				"path": "pages/trend/index"
			},
			{
				"name": "开始评测",
				"path": "pageSheep/sheepTest"
			}
		]
	}, */
	"easycom": {
		"custom": {
			"^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue",
			"^u--(.*)": "uview-plus/components/u-$1/u-$1.vue",
			"^up-(.*)": "uview-plus/components/u-$1/u-$1.vue",
			"^u-([^-].*)": "uview-plus/components/u-$1/u-$1.vue"
		}
	},
	"pages": [{
			"path": "pages/splash",
			"style": {
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/login",
			"style": {
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/register",
			"style": {
				"app-plus": {
					"titleNView": false
				}
			}
		},
		/* 对于小程序，tabbar里的也没必须在pages里，否则运行报错  */
		{
			"path": "pages/home/<USER>",
			"style": {
				"navigationStyle": "default",
				"navigationBarBackgroundColor": "#FFF"
			}
		},
		{
			"path": "pages/privacyAndProtocol",
			"style": {
				"navigationBarTitleText": "title",
				"navigationStyle": "default"
			}
		},

		{
			"path": "pages/trend/index",
			"style": {
				"app-plus": {
					"titleNView": {
						"titleText": "健康趋势" // 设置顶部标题显示的文字
					}
				}
			}
		},
		{
			"path": "pages/device/index",
			"style": {
				"app-plus": {
                    "titleNView": {
						"titleText": "设备"
					}
				}
			}
		},
		{
			"path": "pages/medicineChest/index",
			"style": {
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/personalCentre/index",
			"style": {
				"app-plus": {
					"titleNView": false
				}
			}
		}
	],
	"subPackages": [{
			"root": "pageHealthMeasure",
			"pages": [{
				"path": "historyIndex",
				"style": {
					"navigationBarTitleText": "历史数据"
				}
			}, {
				"path": "manualEntry",
				"style": {
					"navigationBarTitleText": "数据输入"
				}
			}]
		},
		{
			"root": "pageMedicineChest",
			"pages": [{
					"path": "addMedicine",
					"style": {
						"navigationBarTitleText": "添加药品"
					}
				}, {
					"path": "addSearchMedicine",
					"style": {
						"navigationBarTitleText": "药物查找"
					}
				},
				{
					"path": "changeFrequency",
					"style": {
						"navigationBarTitleText": "用药频率"
					}
				},
				{
					"path": "medicineTime",
					"style": {
						"navigationBarTitleText": "服药时间"
					}
				},
				{
					"path": "boxMedicine",
					"style": {
						"navigationBarTitleText": "管理用药"
					}
				}
			]
		},
		{
			"root": "pageInfo",
			"pages": [{
				"path": "baseInfo",
				"style": {
					"navigationBarTitleText": "完善信息"
				}
			}, {
				"path": "uploadInfo",
				"style": {
					"navigationBarTitleText": "上传报告"
				}
			}]
		},
		{
			"root": "pageSheep",
			"pages": [{
				"path": "resultList",
				"style": {
					"navigationBarTitleText": "睡眠评测"
				}
			}, {
				"path": "sheepTest",
				"style": {
					"navigationBarTitleText": "开始评测"
				}
			}, {
				"path": "sheepTestResult",
				"style": {
					"navigationBarTitleText": "评测结果"
				}
			}, {
				"path": "pittsburghSleepWiki",
				"style": {
					"navigationBarTitleText": "了解匹兹堡睡眠质量指标"
				}
			}]
		},
		{
			"root": "pageChronicDisease",
			"pages": [{
				"path": "target",
				"style": {
					"navigationBarTitleText": "慢性病指标"
				}
			}, {
				"path": "bloodPressureTestPlan",
				"style": {
					"navigationBarTitleText": "评测计划"
				}
			}]
		},
		{
			"root": "pageFamily",
			"pages": [{
				"path": "familyMenber",
				"style": {
					"navigationBarTitleText": "选择家庭成员"
				}
			}, {
				"path": "addFamilyMenber",
				"style": {
					"navigationBarTitleText": "添加家庭成员"
				}
			}, {
				"path": "chronicDisease",
				"style": {
					"navigationBarTitleText": "选择管理类型"
				}
			}]
		},
        {
			"root": "pageDevice",
			"pages": [{
				"path": "addDevice",
				"style": {
					"navigationBarTitleText": "添加设备"
				}
			}]
		},
        {
			"root": "pageUseInfo",
			"pages": [{
				"path": "useManual",
				"style": {
					"navigationBarTitleText": "用户手册"
				}
			}, {
				"path": "usePolicy",
				"style": {
					"navigationBarTitleText": "隐私条款"
				}
			}
            , {
				"path": "useDeviceList",
				"style": {
					"navigationBarTitleText": "设备管理"
				}
			}
            , {
				"path": "setList",
				"style": {
					"navigationBarTitleText": "通用设置"
				}
			}
            , {
				"path": "about",
				"style": {
					"navigationBarTitleText": "关于我们"
				}
			}
            , {
				"path": "historicalArchives",
				"style": {
					"navigationBarTitleText": "历史档案"
				}
			}
            , {
				"path": "appdicom",
				"style": {
					"navigationBarTitleText": "图像预览"
				}
			}
            ]
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "NEED健康",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8"
	},
	"tabBar": {
		"color": "#9d9d9d",
		"selectedColor": "#414141",
		"backgroundColor": "",
		"position": "bottom",
		"borderStyle": "white",
		"list": [{
				"pagePath": "pages/home/<USER>",
				"text": "首页",
				"iconPath": "static/navigator/Home.png",
				"selectedIconPath": "static/navigator/Home_HL.png"
			},
			{
				"pagePath": "pages/trend/index",
				"text": "趋势 ",
				"iconPath": "static/navigator/trend.png",
				"selectedIconPath": "static/navigator/trend_HL.png"
			},
			{
				"pagePath": "pages/device/index",
				"text": "设备",
				"iconPath": "static/navigator/device.png",
				"selectedIconPath": "static/navigator/device_HL.png"
			}, {
				"pagePath": "pages/medicineChest/index",
				"text": "药箱",
				"iconPath": "static/navigator/medicineChest.png",
				"selectedIconPath": "static/navigator/medicineChest_HL.png"
			},
			{
				"pagePath": "pages/personalCentre/index",
				"text": "我的",
				"iconPath": "static/navigator/PersonalCentre.png",
				"selectedIconPath": "static/navigator/PersonalCentre_HL.png"
			}
		]
	}
}