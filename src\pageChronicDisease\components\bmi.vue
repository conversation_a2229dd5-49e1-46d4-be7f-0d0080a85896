<template>
	<view class="flex flex-col items-stretch m-[28rpx] px-[20rpx] py-[28rpx]">

		<view class="bg-white rounded-[16rpx] m-[15rpx] p-[30rpx]">
			<view class="flex items-center justify-between">
				<text class="text-base text-[#333]">体脂</text>
			</view>

			<view class="charts-box">
				<qiun-data-charts type="area" :opts="chartsOpts" :chartData="chartData" />
			</view>
		</view>

		<view class="grid grid-cols-2">
			<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[24rpx] py-[24rpx]" @click="gotoManualEntry(3)">
				<text class="font-bold">手动输入</text>
			</view>

			<!-- @click="gotoMeasure" -->
			<view class="flex flex-col items-center bg-[#ddd] rounded-[16rpx] m-[24rpx] py-[24rpx]">
				<text class="font-bold">测量体重</text>
			</view>
		</view>

		<view class="pt-[36rpx] pb-[12rpx] font-bold">血体脂记录</view>

		<view class="flex flex-col mt-[8rpx] rounded-[16rpx] bg-[#fff]">
			<view class="flex flex-row justify-between list-border p-[24rpx]" v-for="(item, index) in respInfo.recordList"
				:key="index ">
				<text>{{ item.date }}</text>
				<text>{{ item.weight || "-" }}Kg</text>
				<text>{{ item.bmi || "-" }}%</text>
			</view>
		</view>

	</view>
</template>

<script setup lang="ts">
	import { onLoad, onShow, onReady } from "@dcloudio/uni-app";
	import { ref, reactive, computed, watch, onMounted } from 'vue';
	import { getBloodPressureOptions, getPulseValueOptions } from '@/model_static/bloodPresure'
	import type { IHealthObsResp } from '@/models/healthMeasure'
	import type { CommonTag } from '@/models/common'
	import {
		getMonthTagList,
		fillKeyNameByTypeData
	} from "@/model_static/history";

	import {
		getDateMeasureRecord,
		getMonthMeasureRecord
	} from '@/common/api/measure'
	import { nextTick } from 'process';

	/* const props = withDefaults(
		defineProps<{
			viewMode : string;
			dataType : string;
		}>(),
		{
			//没有默认值就是必填项
		}
	); */

	interface MySubmitForm {
		chart : object
		recordList : {
			date : string
			weight : string
			bmi : string
		}[]
	}
	const respInfo = ref<MySubmitForm>(
		{
			chart: {},
			recordList: [{
				date: "2024-12-06",
				weight: "52.4",
				bmi: "29"
			},{
				date: "2024-12-07",
				weight: "52.2",
				bmi: "29"
			},{
				date: "2024-12-08",
				weight: "50.9",
				bmi: "28"
			}]
		})

	//早上和晚上共用opt
	const chartsOpts = ref<any>({
		color: ['#188cf7', '#93c978'],
		yAxis: {
			/* data: [{ min: 0, max: 300 }],
			splitNumber: 5 */
		},
		padding: [10, 10, 0, 6],
		legend: { show: true },
		extra: {
			area: {
				type: "curve",
				opacity: 0.2,
				addLine: true,
				width: 2,
				gradient: true,
				activeType: "hollow"
			}
		},
	});

	const chartData = ref();

	const updateChart = () => {
		console.log("updateMornig: ");
		chartData.value = {
			"categories": ["12-06", "12-07", "12-08", "12:09", "12-10", "12-11"],
			"series": [{
				"name": "体脂率",
				"data": [24, 26, 31, 25, 30, 30]
			}, {
				"name": "体重",
				"data": [51, 56, 50, 54, 56, 58]
			}]
		};
	}

	const gotoManualEntry = (index :number) => {
		// 血压=0； 体温=1； 血糖=2；体脂=3
		console.log("gotoManualEntry: ",index);
		uni.navigateTo({
			url: "/pageHealthMeasure/manualEntry" + "?params=" + index
		})
	}

	const gotoMeasure = () => {
		console.log("gotoMeasure: ");

	}

	const gotoPlan = () => {
		console.log("gotoPlan: ");

	}

	/* watch(
		() => props.viewMode,
		(value) => {
			console.log('props.viewMode', props.viewMode);

		},
		{ deep: false, immediate: true }
	); */
	onMounted(() => {
		// 组件能被调用必须是组件的节点已经被渲染到页面上
		setTimeout(() => {
			updateChart();
		}, 300);
	})
</script>

<style lang="scss">
	.u-page__tag-item {
		margin-right: 20px;
	}

	.charts-box {
		width: 100%;
		height: 500rpx;
	}
</style>