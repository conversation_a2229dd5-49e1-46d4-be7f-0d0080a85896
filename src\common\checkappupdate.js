
export function downloadNewVersion(downAppUrl) {
    let view = new plus.nativeObj.View("maskView", {
        backgroundColor: "rgba(0,0,0,.5)",
        left: ((plus.screen.resolutionWidth / 2) - 60) +
            "px",
        bottom: "80px",
        width: "120px",
        height: "34px"
    })

    var dtask = plus.downloader.createDownload(downAppUrl, {
        filename: '_doc/update/' + new Date().getTime() + '/'
    },function (d, status) {
        // 下载完成
        if (status == 200) {
            plus.runtime.install(d.filename, {
                force: true
            }, function () {
                uni.showModal({
                    title: '安装完成',
                    content: '已完成更新，点击重启应用',
                    showCancel: false,
                    confirmText: '重启',
                    success: () => {
                        //进行重新启动;
                        plus.runtime.restart();
                    },
                })
            }, (e) => {
                uni.showToast({
                    title: 'install fail:' + JSON
                        .stringify(e),
                    icon: 'none'
                })
                view.hide()
                console.log(JSON.stringify(e))
            });
        } else {
            uni.showToast("下载错误 code: " + status);
        }
    });

    view.drawText('开始下载...', {}, {
        size: '14px',
        color: '#FFFFFF'
    });
    view.show()
    // console.log(dtask);
    dtask.addEventListener("statechanged", (e) => {
        if (e && e.downloadedSize > 0) {
            let jindu = ((e.downloadedSize / e.totalSize) *
                100).toFixed(2)
            view.reset();
            view.drawText('下载:' + jindu + '%', {}, {
                size: '14px',
                color: '#FFFFFF'
            });
        }
    }, false);
    dtask.start();
}
export default function checkappupdate(param = {}) {

    // console.log(param)
    // console.log(plus)
    plus.runtime.getProperty(plus.runtime.appid, (widgetInfo) => {
        console.log(widgetInfo)
        const db = uniCloud.database();
        db.collection('version-update').limit(1).get().then(res => {

            // 不存在则不提示
            if (!res.result || !res.result.data) {
                return
            }

            const data = res.result.data[0]

            let versionCode = parseInt(widgetInfo.versionCode)
            let downAppUrl = data.url

            // 不需要提醒更新
            if (!data.remind) {
                return
            }

            // 版本号比较
            if (versionCode >= data.versionCode) {
                return
            }
            const not_update_app = uni.getStorageSync('not_update_app')

            // 不提示新这个版本
            if (!data.force && not_update_app && not_update_app.versionCode === data.versionCode) {
                return
            }

            //升级提示
            uni.showModal({
                title: '升级版本' + data.version,
                content: data.info,
                showCancel: data.force ? false : true,
                confirmText: '更新APP',
                cancelText: '暂不',
                success: res => {
                    if (!res.confirm) {
                        uni.setStorageSync('not_update_app', {
                            time: new Date().getTime(),
                            versionCode: data.versionCode,
                            platform: plus.os.name,
                        })

                        uni.showToast({title: '已取消，本次更新将不会提醒', icon: 'none'})
                        // plus.runtime.quit();
                        return
                    }
                    // if (data.shichang === 1) {
                    // 	//去应用市场更新
                    // 	plus.runtime.openURL(data.shichangurl);
                    // 	plus.runtime.restart();
                    // } else {
                    // 开始下载
                    // 创建下载任务
                    downloadNewVersion(downAppUrl)
                },
                fail(e) {
                    console.log(e);
                    uni.showToast({
                        title: '请求错误'
                    })
                }
            })
        }).cacth(err => {
            console.log(err)
        })
    })
}