<template>
	<view class="flex-col">                
		<z-paging ref="paging" :refresher-enabled="false" :loading-more-enabled="false" :safe-area-inset-bottom="true">
			<!-- 之后-vue3 -->
			<template #top>
				<view class="flex-col">
					<view class="flex justify-center pt-[30rpx] pb-[20rpx]" style="width: 100%;">
						<view style="width: 200rpx" />
						<up-subsection :list="['日','月']" :current="segmentedIndex" fontSize="14" mode="subsection"
							activeColor="#237FC2" @change="onClickSegmentedItem"></up-subsection>        
						<view style="width: 200rpx" />
					</view>

					<view class="p-[20rpx]">
						<u-tabs :list="typeList" keyName="typeName" :current="curTabIndex" @click="tabItemClick"></u-tabs>
					</view>
				</view>
			</template>

			<typeFragment class="mt-[20rpx]" :viewMode="viewMode" :dataType="queryDataType" />
		</z-paging>
	</view>
</template>

<script setup lang="ts">
	import { onMounted, ref, reactive } from "vue";
	import { onLoad, onShow } from "@dcloudio/uni-app";

	import type { IHealthObsResp } from '@/models/healthMeasure'
	import typeFragment from "./components/typeFragment.vue";
	import type { S } from "vite/dist/node/types.d-aGj9QkWt";

	const firstLoadType = ref<string>();
	const params = ref<IHealthObsResp>();
	const segmentedIndex = ref<number>(0);

	// 创建响应式数据  
	const typeList = reactive<IHealthObsResp[]>([
		{
			type: "1",
			id: "1",
			typeName: "血压",
			icon: "",
			datetime: "11-20 10:35",
			//require: [''],
			measureValue: "135/83",
			measureValueUnit: "mmHg",
			pulse: "80",//脉搏，血压测量专用
			measureResultFlag: ""
		},
		{
			type: "2",
			id: "2",
			typeName: "血氧",
			icon: "",
			datetime: "11-20 10:35",
			require: ['餐前'],
			measureValue: "10.8",
			measureValueUnit: "%SpO2",
			pulse: "",//脉搏，血压测量专用
			measureResultFlag: ""
		},
		{
			type: "3",
			id: "3",
			typeName: "体温",
			icon: "",
			datetime: "11-20 10:35",
			//require: [''],
			measureValue: "36.9",
			measureValueUnit: "℃",
			pulse: "",//脉搏，血压测量专用
			measureResultFlag: ""
		},
		{
			type: "4",
			id: "4",
			typeName: "心率",
			icon: "",
			datetime: "11-20 10:35",
			//require: [''],
			measureValue: "80",
			measureValueUnit: "次/分",
			pulse: "",//脉搏，血压测量专用
			measureResultFlag: ""
		},
		{
			type: "5",
			id: "5",
			typeName: "脉率",
			icon: '',
			datetime: "11-20 10:35",
			//require: [''],
			measureValue: "83",
			measureValueUnit: "次/分",
			pulse: "",//脉搏，血压测量专用
			measureResultFlag: ""
		},
		{
			type: "6",
			id: "6",
			typeName: "体重",
			icon: '',
			datetime: "11-20 10:35",
			//require: [''],
			measureValue: "-",
			measureValueUnit: "kg",
			pulse: "",//脉搏，血压测量专用
			measureResultFlag: ""
		},
		{
			type: "7",
			id: "7",
			typeName: "血糖",
			icon: "",
			datetime: "11-20 10:35",
			//require: [''],
			measureValue: "-",
			measureValueUnit: "mmol/L",
			pulse: "",//脉搏，血压测量专用
			measureResultFlag: ""
		}
	]);
	const curTabIndex = ref<number>(0);
	const viewMode = ref<string>('day');
	const queryDataType = ref<string>('');

	// 定义方法  
	function tabItemClick(item : any) {
		console.log('item', item);
		queryDataType.value=item.type;
	}

	onLoad((option : any) => {
		//{"type":"1"}
		console.log('HISTORY_INDEX【onLoad】：页面加载完成', JSON.stringify(option));
		firstLoadType.value = option.params;
		console.log('firstLoadType', firstLoadType.value);

		for (let i = 0; i < typeList.length; i++) {
			console.log('typeList[0]'+typeList[0].type);
			if (typeList[i].type === firstLoadType.value) {
				curTabIndex.value = i;
				console.log('curTabIndex.value~~~'+curTabIndex.value);
				queryDataType.value=typeList[i].type;
				console.log('queryDataType.value~~~'+queryDataType.value);
				break
			}
		}
	})

	onShow(() => {
		console.log("HISTORY_INDEX【onShow】：页面重新可见");

		//检查token情况

	});

	const onClickSegmentedItem = (item : any) => {
		console.log('onClickSegmentedItem', item);
		viewMode.value = item === 0 ? 'day' : 'month';
		segmentedIndex.value = item;
	}
</script>

<style scoped lang="scss">
	.calendar_container {
		min-height: calc(100vh - var(--window-top));
		background-color: #f5f5f5;
		padding: 30rpx;
		box-sizing: border-box;
	}
</style>