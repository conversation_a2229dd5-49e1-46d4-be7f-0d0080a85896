'use strict';
const crypto = require('crypto');

const baseUrl = 'https://need.service.i-need.com.cn:39002'

// 密钥（Hex 格式）
const keyHex = '4b6a54424531784e4842475676555855';
const key = Buffer.from(keyHex, 'hex');

// 使用 AES-ECB 加密
function aesEncryptECB(plaintext, key) {
  // 创建加密器（指定算法和模式）
  const cipher = crypto.createCipheriv('aes-128-ecb', key, null); // ECB 模式无需 IV
  cipher.setAutoPadding(true); // 启用自动填充（PKCS5Padding）

  // 加密并输出结果
  let encrypted = cipher.update(plaintext, 'utf8', 'base64');
  encrypted += cipher.final('base64');

  return encrypted;
}

exports.main = async (event, context) => {
	
	let phoneNumber = event.phoneNumber
	
	if (event.access_token) {
		const res = await uniCloud.getPhoneNumber({
			appid: context.APPID, // 客户端callFunction时携带的AppId信息
			provider: 'univerify',
			access_token: event.access_token,
			openid: event.openid
		})
		phoneNumber = res.phoneNumber
	}
	
	if (!phoneNumber) {
		return {
			success: false,
			code: 0,
			data: {},
			msg: '获取手机失败！'
		}
	}
	
	// 待加密的明文
	const plaintext = '12345612111-'+ phoneNumber +'-13147777444';
	// 执行加密
	const encryptedText = aesEncryptECB(plaintext, key);
	// 请求后端一键登录
	const apiUrl = baseUrl + '/monitor/need/login/oneClickLogin'
	const loginRes = await uniCloud.httpclient.request(apiUrl, {
		method: 'POST',
		data: {
			encryptedPhoneNumber: encryptedText,
		},
		contentType: 'application/x-www-form-urlencoded',
		dataType: 'json' // 指定返回值为json格式，自动进行parse
	})
	
	console.log(loginRes)

	if (!loginRes.data.success) {
		return loginRes.data
	}
	
	const { data: loginData } = loginRes.data
	
	if (!loginData) {
		return {
			success: false,
			code: 0,
			data: {},
			msg: '请求登录失败！'
		}
	}
	console.log(loginData)
	// 获取家庭成员
	const personRes = await uniCloud.httpclient.request(baseUrl + '/monitor/need/familyPerson/getPersonById', {
		method: 'POST',
		data: {
			personId: loginData.personId,
		},
		headers: {
			token: loginData.token
		},
		contentType: 'application/json', // 指定以application/json发送data内的数据
		dataType: 'json' // 指定返回值为json格式，自动进行parse
	})
	
	const { data: personData } = personRes.data
	
	if (!personData) {
		return {
			success: false,
			code: 0,
			data: {},
			msg: '请求获取成员失败！'
		}
	}
	
	// 返回登录信息
	loginData.personInfo = personData
	// const data = Object.assign({}, loginData, personData)
	return {
		success: true,
		code: 0,
		data: loginData,
		msg: '获取成功！'
	}
};
