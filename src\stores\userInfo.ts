/*
 * @Description:
 * @Author: shuliang
 * @Date: 2022-06-27 15:57:20
 * @LastEditTime: 2022-06-27 16:17:59
 * @LastEditors: shuliang
 */
import { defineStore } from 'pinia'
import type { UserInfosStates, ILoginResp } from '@/models/login'

/**
 * 用户信息
 * @methods setUserInfos 设置用户信息
 */
export const useUserInfo = defineStore('userInfo', {
	state: () : UserInfosStates => ({
		userInfos: uni.getStorageSync('userInfo') || {
            personId: "",
			userId: "",
			token: ".",
			personInfo: {},
		},
		token: uni.getStorageSync('token'),
	}),
    getters: {
        userId: (state) => state.userInfos?.userId,  
    },
	actions: {
		async setUserInfos(userInfos : ILoginResp) {
			// 存储用户信息到浏览器缓存
			uni.setStorageSync('userInfo', userInfos)
			this.userInfos = userInfos
		},
		async setToken(token : string) {
			// 存储用户信息到浏览器缓存
			uni.setStorageSync('token', token)
			this.token = token
		},

		async removeUserInfos() {
			// 存储用户信息到浏览器缓存
			uni.removeStorageSync('userInfo')
			this.userInfos = {
				personId: "",
                userId: "",
                token: ".",
                personInfo: {},
			}
		},
		async removeToken() {
			// 存储用户信息到浏览器缓存
			uni.removeStorageSync('token')
			this.token = ""
		},
	},
})