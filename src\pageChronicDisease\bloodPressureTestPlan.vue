<template>
	<view class="flex flex-col">

		<view class="flex flex-col bg-white rounded-[16rpx] m-[28rpx] p-[28rpx]">
			<view class="p-[20rpx] font-bold">血压管理</view>
			<view class="flex flex-col mx-[24rpx] my-[8rpx] bg-[#ccc]" style="height: 2rpx;" />

			<view class="flex flex-col flex-1 bg-[#f0f0f0] rounded-[16rpx] m-[16rpx] p-[28rpx]">
				<view class="flex flex-row py-[12rpx]">
					<view class="flex flex-row flex-1 items-center">
						<text class="font-bold">起床后测量</text>
						<view class="text-xl font-bold text-[#ff1111] pl-[16rpx]">
							*
						</view>
					</view>
					<view class="flex flex-row flex-1 items-center">
						<view class="flex-1 pl-[36rpx]">
							<!-- <uni-datetime-picker type="time" :border="false" v-model="submitForm.morningTime" /> -->

							<picker mode="time" :value="submitForm.morningTime" @change="morningChange">
								<text class="font-bold">{{ submitForm.morningTime || '选择时间' }}</text>
							</picker>
						</view>
						<uni-icons type="right" size="32" color="#014777"></uni-icons>
					</view>
				</view>

				<view class="flex flex-row flex-1 items-center py-[12rpx]">
					<view class="flex flex-row flex-1 items-center">
						<text class="font-bold">睡前测量</text>
						<view class="text-xl font-bold text-[#ff1111] pl-[16rpx]">
							*
						</view>
					</view>
					<view class="flex flex-row flex-1 items-center">
						<view class="flex-1 pl-[36rpx]">
							<picker mode="time" :value="submitForm.nightTime" @change="nightChange">
								<text class="font-bold">{{ submitForm.nightTime || '选择时间' }}</text>
								<!-- <up-input v-model="submitForm.nightTime" placeholder="选择时间" border="none" readonly /> -->
							</picker>
						</view>

						<uni-icons type="right" size="32" color="#014777"></uni-icons>
					</view>
				</view>
			</view>

			<view class="p-[20rpx] font-bold">血压测量频率</view>
			<view class="flex flex-col mx-[24rpx] my-[8rpx] bg-[#ccc]" style="height: 2rpx;" />
			<view class="flex flex-col p-[20rpx]">
				<up-grid :border="false" col="4" gap="12rpx" align="center">
					<up-grid-item v-for="(item,index) in weekDays" :key="index">
						<up-tag class="my-[12rpx]" style="min-width: 48rpx;" :text="item.text" shape="circle"
							:bgColor="item.checked ? '#0081ff' : '#ccc'" :borderColor="item.checked ? '#0081ff' : '#ccc'"
							@click="weekRadioClick(index)"> </up-tag>
					</up-grid-item>
				</up-grid>
			</view>

			<view class="flex flex-row justify-between p-[20rpx]">
				<view class="flex flex-row items-center">
					<text class="font-bold">开启提醒</text>
					<view class="text-2xl font-bold text-[#ff1111] pl-[16rpx]">
						*
					</view>
				</view>

				<view class="flex flex-row items-center">
					<up-switch v-model="submitForm.tipsActive"> </up-switch>
				</view>
			</view>

		</view>

		<button class="my-2" type="primary" style="width: 50%; background-color: #2B97E5; color: white;"
			@click="onSubmitClick">提交</button>
	</view>
</template>

<script setup lang="ts">
	import { onLoad, onShow, onReady } from "@dcloudio/uni-app";
	import { ref, reactive, computed, watch, onMounted } from 'vue';
	import { getBloodPressureOptions, getPulseValueOptions } from '@/model_static/bloodPresure'
	import type { IHealthObsResp } from '@/models/healthMeasure'
	import type { CommonTag } from '@/models/common'
	import {
		getMonthTagList,
		fillKeyNameByTypeData
	} from "@/model_static/history";

	import {
		getDateMeasureRecord,
		getMonthMeasureRecord
	} from '@/common/api/measure'
	import { nextTick } from 'process';

	const showMorningPicker = ref(false)
	const showNightPicker = ref(false)
	interface MySubmitForm {
		morningTime : string
		nightTime : string
		rate : string
		tipsActive : boolean
	}

	const submitForm = ref<MySubmitForm>(
		{
			morningTime: "",
			nightTime: "",
			rate: "",
			tipsActive: false
		})

	const morningChange = (e : any) => {
		console.log("pickerViewChange", e);
		submitForm.value.morningTime = e.detail.value
	}
	
	const nightChange = (e : any) => {
		console.log("pickerViewChange", e);
		submitForm.value.nightTime = e.detail.value
	} 

	// 每周特定日期选项
	const weekDays = ref<CommonTag[]>([{
		text: '周一',
		value: 0,
		checked: false,
		disable: false
	}, {
		text: '周二',
		value: 1,
		checked: false,
		disable: false
	}, {
		text: '周三',
		value: 2,
		checked: false,
		disable: false
	}, {
		text: '周四',
		value: 3,
		checked: false,
		disable: false
	}, {
		text: '周五',
		value: 4,
		checked: false,
		disable: false
	}, {
		text: '周六',
		value: 5,
		checked: false,
		disable: false
	}, {
		text: '周日',
		value: 6,
		checked: false,
		disable: false
	}]);
	let weekDayResult : any[] = [];

	const weekRadioClick = (myIndex : number) => {
		console.log('radioClick', myIndex);
		weekDayResult = [];
		weekDays.value.forEach((item, index) => {
			if (index === myIndex) {
				item.checked = !item.checked
			} /* else{
				item.checked = true
			} */


			if (item.checked) {
				weekDayResult.push(item.text);
			}
		});

		console.log(weekDays.value);
	};

	const changeConfirmMorning = (e) => {
		console.log('change', e);
		submitForm.value.morningTime = String(e.value[0]);
		showMorningPicker.value = false;
	};

	const changeConfirmNight = (e) => {
		console.log('change', e);
		submitForm.value.nightTime = String(e.value[0]);
		showNightPicker.value = false;
	};

	const onSubmitClick = () => {
		console.log("onSubmitClick: ");

	}

	onMounted(() => {
		// 组件能被调用必须是组件的节点已经被渲染到页面上

	})
</script>

<style lang="scss">
	.u-page__tag-item {
		margin-right: 20px;
	}

	.charts-box {
		width: 100%;
		height: 500rpx;
	}
</style>