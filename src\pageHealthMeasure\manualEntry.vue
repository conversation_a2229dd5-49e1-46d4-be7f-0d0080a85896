<template>
	<view class="flex-col">
		<z-paging ref="paging" :refresher-enabled="false" :loading-more-enabled="false" :safe-area-inset-bottom="true">
			<!-- 之后-vue3 -->
			<template #top>
				<view class="m-[30rpx] flex justify-center">
					<u-tabs :list="types" :activeStyle="{
					    color: '#014777',
					fontSize:'46rpx',
					    transform: 'scale(1.05)'
					}" :inactiveStyle="{
					    color: '#333',
					fontSize:'36rpx',
					    transform: 'scale(1)'
					}" :current="curTabIndex" @click="tabItemClick"></u-tabs>
				</view>

			</template>

			<bloodPressure v-show="curTabIndex === 0" class="mt-[20rpx]" />
			<temp v-show="curTabIndex === 1" class="mt-[20rpx]" />
			<bloodSugar v-show="curTabIndex === 2" class="mt-[20rpx]" />
			<bmi v-show="curTabIndex === 3" class="mt-[20rpx]" />
		</z-paging>
	</view>
</template>

<script setup lang="ts">
	import { onMounted, ref, reactive } from "vue";
	import { onLoad, onShow } from "@dcloudio/uni-app";

	import type { IHealthObsResp } from '@/models/healthMeasure'
	import bloodPressure from "./components/manualEntry/bloodPressure.vue";
	import temp from "./components/manualEntry/temp.vue";
	import bloodSugar from "./components/manualEntry/bloodSugar.vue";
	import bmi from "./components/manualEntry/bmi.vue";

	const params = ref<IHealthObsResp>();
	const firstLoadType = ref<string>();

	const types = reactive([
		{ name: '血压' },
		{ name: '体温' },
		{ name: '血糖' },
		{ name: '体脂' }
	]);
	const curTabIndex = ref<number>(0);
	const viewMode = ref<string>('day');

	// 定义方法  
	function tabItemClick(item : any) {
		console.log('item', item);
		curTabIndex.value = item.index;
	}

	onLoad((option : any) => {
		//{"type":"1"}
		console.log('MANUAL_ENTRY【onLoad】：页面加载完成', JSON.stringify(option));
		firstLoadType.value = option.params;
		console.log('firstLoadType', firstLoadType.value);
		
	})
	
	onMounted(() => {
		// 组件能被调用必须是组件的节点已经被渲染到页面上
		setTimeout(() => {
			 curTabIndex.value = Number(firstLoadType.value);
		}, 300)
	});

	onShow(() => {
		console.log("MANUAL_ENTRY【onShow】：页面重新可见");


	});
</script>

<style scoped lang="scss">

</style>