<template>
	<view class="p-[30rpx]">
		<view class="bg-white rounded-[20rpx] p-[30rpx] mt-[30rpx]">
			<uni-forms ref="customForm" :modelValue="formData" :rules="customRules" :label-width="100"
				class="mt-[30rpx] px-[20rpx]">
				<uni-forms-item label="药物名称" name="medicineName">
					<input v-model="formData.medicineName" class="!w-auto h-35px !py-0px ![border:1px_solid_#e5e5e5] !font-size-14px" placeholder="请选择药品" style="" @click="onClickSearch" />
				</uni-forms-item>
				<uni-forms-item label="药品类型">
					<uni-data-picker v-model="formData.medicineType" :localdata="typeData" popup-title="选择药品类型"
						@change="typeChanged">
					</uni-data-picker>
				</uni-forms-item>
				<uni-forms-item label="过期日期">
					<uni-datetime-picker type="date" return-type="date" v-model="formData.expireTime" />
				</uni-forms-item>
				<uni-forms-item label="开始日期" name="startTime" >
					<uni-datetime-picker type="date" return-type="date" v-model="formData.startTime" />
				</uni-forms-item>
				<uni-forms-item label="用药天数", name="days">
					<input v-model="formData.days" placeholder="请选择用药天数" class="!w-auto h-35px !py-0px ![border:1px_solid_#e5e5e5] !font-size-14px" disabled
						@click="inputDialogToggle" />
				</uni-forms-item>
				<uni-forms-item label="用药频率">
					<input v-model="formData.dayIntervalText" class="!w-auto h-35px !py-0px ![border:1px_solid_#e5e5e5] !font-size-14px" placeholder="请选择" disabled
						@click="frequencyChange" />
				</uni-forms-item>
				<uni-forms-item label="服药时间">
					<input v-model="formData.planTime" placeholder="请选择" class="!w-auto h-35px !py-0px ![border:1px_solid_#e5e5e5] !font-size-14px" disabled @click="timeChange" />
				</uni-forms-item>
				<uni-forms-item label="每次剂量" class="relative" name="perDose">
					<input v-model="formData.perDose" placeholder="请选择" class="!w-auto h-35px !py-0px ![border:1px_solid_#e5e5e5] !font-size-14px" @click="" />
					<view class='absolute right-30 top-15'>{{ formData.inventoryUnit}}</view>
				</uni-forms-item>
				<uni-forms-item label="药物说明书">
					<view class='flex flex-row'>
						<view class='flex-1'></view>
						<uni-icons type='forward' size='23'></uni-icons>
					</view>
				</uni-forms-item>

			</uni-forms>

		</view>
		<view class="bg-white rounded-[20rpx] p-[30rpx] mt-[30rpx]">
			<uni-forms :label-width="120">
				<uni-forms-item label="开启提醒用药">
					<view class='flex flex-row'>
						<view class='flex-1'></view>
						<switch name="switch" />
					</view>
				</uni-forms-item>
			</uni-forms>
		</view>
		<view>
			<button @click="submit()" size="default" class="text-[#fff] bg-[#2B97E5] rounded-[50rpx] mt-[50rpx]">
				保存
			</button>
		</view>

		<view>
			<!-- 输入框示例 -->
			<uni-popup ref="inputDialog" type="dialog">
				<uni-popup-dialog ref="inputClose" type="dialog" mode="input" title="用药天数" value="对话框预置提示内容!"
					placeholder="请输入内容" @confirm="dialogInputConfirm">
					<!-- 用药天数选择 -->
					<view class="medicine-duration">
						<!-- 动态展示选择的天数 -->
						<view class="tag-options">
							<view class="rounded-lg bg-[#EFEFEF] p-2 text-center m-2" v-for="(item, index) in durations" :key="index"
								@click="handleDurationChange(item)">
								{{item}}
							</view>
						</view>

						<!-- 输入框，根据选中的天数动态更新 -->
						<view class="custom-input">
							<input type="number" v-model="customDuration" :placeholder="`请输入天数`" />
						</view>
					</view>
				</uni-popup-dialog>
			</uni-popup>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { ref } from 'vue';
	import { onLoad, onShow, onUnload } from "@dcloudio/uni-app";
	import moment from 'moment';
    import { createMedicinePlan } from '@/common/api/medicine'
	// 获取对话框的引用
	const inputDialog = ref<HTMLElement | null>(null); // uni-popup的引用
	const inputClose = ref<HTMLElement | null>(null); // uni-popup-dialog的引用

	let selectDrug : any = {};
	// 预设的天数选项（包括长期）
	const durations = ['1天', '3天', '7天', '15天', '30天', '90天', '长期'];

	// 选中的天数
	const selectedDuration = ref<string>('1天');

	// 自定义天数输入（当选择"长期"时显示）
	const customDuration = ref<number | null>(1); // 默认值为1天
	// 校验表单数据
	const formData = ref({
		medicineName: '',
		medicineType: 1,
		expireTime: moment().add(6, 'months').format('YYYY-MM-DD'),
		startTime: moment().format('YYYY-MM-DD'),
		days: '',
        intervalType: 1,
        dayInterval: 1,
        dayIntervalText: '每天',
        // dayInterval: null,
		planTime: '07:00,12:30,18:00',
		perDose: 1,
		inventoryUnit: '粒'
	});
	// 药物类型
	const typeData = [{
		text: "胶囊",
		unit: "粒",
		value: 1,
	}, {
		text: "药片",
		unit: "片",
		value: 2,
	}, {
		text: "软膏",
		unit: "次涂抹",
		value: 3,
	}, {
		text: "液体",
		unit: "ml",
		value: 4,
	}];
    // , {
	// 	text: "设备",
	// 	unit: "台",
	// 	value: "10006",
	// }
	// 自定义表单校验规则
	const customRules = {
		medicineName: {
			rules: [{
				required: true,
				errorMessage: '药物名称不能为空'
			}]
		},
		days: {
			rules: [{
				required: true,
				errorMessage: '用药天数不能为空'
			}]
		},
		startTime: {
			rules: [{
				required: true,
				errorMessage: '开始日期'
			}]
		},
		dayInterval: {
			rules: [{
				required: true,
				errorMessage: '用药频率不能为空'
			}]
		},
		perDose: {
			rules: [{
				required: true,
				errorMessage: '用药剂量不能为空'
			}]
		},
	};

	const typeChanged = (e : any) => {
		console.log('onchange:', e);
		let res = typeData.filter((element) => element.value == e.detail.value[0].value).map((item) => item.unit);

		console.log('res:', res);
		if (res && res[0]) {
			formData.value.inventoryUnit = res[0];
		}

	};

	const inputDialogToggle = () => {
		// 通过ref引用打开对话框
		if (inputDialog.value) {
			inputDialog.value.open();
		}
	};
	const frequencyChange = () => {
		uni.navigateTo({
			url: "/pageMedicineChest/changeFrequency",
			animationType: "slide-in-right",
			animationDuration: 300,
		})
	}
	const timeChange = () => {
		uni.navigateTo({
			url: '/pageMedicineChest/medicineTime',
			animationType: "slide-in-right",
			animationDuration: 300,
		})
	}
	// 处理用药天数选择变化
	const handleDurationChange = (item : string) => {
		selectedDuration.value = item;

		if (item === '长期') {
			// 如果选择"长期"，自动填充1095天
			customDuration.value = 1095;
		} else {
			// 如果选择的是其他天数，自动填充对应天数
			const days = parseInt(item);
			customDuration.value = days;
		}
	};
	const dialogInputConfirm = () => {
		setTimeout(() => {
			uni.hideLoading();
			console.log(customDuration.value);
			formData.value.days = customDuration.value
			// 关闭对话框
			if (inputDialog.value) {
				inputDialog.value.close();
			}
		}, 300);
	};
	const customForm = ref();
	const onClickSearch = () => {
		uni.navigateTo({
			url: "/pageMedicineChest/addSearchMedicine"
		})
	}

    const getCurrentMemberInfo = () => {
        console.log('历史数据页面加载onShow完成')
		let lastUserInfo = uni.getStorageSync('lastSelectedUserInfo');
		let userInfo = uni.getStorageSync('userInfo');
		if (!lastUserInfo || !lastUserInfo.personId) {
			if (!userInfo || !userInfo.personId) {
				uni.showModal({
					content: "用户获取失败，请重新登录",
					success: (res) => {
						if (res.confirm) {
							uni.reLaunch({
								url: '/pages/login'
							})
						}
					}
				})
				return
			} else {
				return { personId: userInfo.personId, personName: userInfo.personInfo.personName }
			}
		} else {
			return { personId: lastUserInfo.personId, personName: lastUserInfo.personName }
		}
    }

	const submit = () => {
        console.log(formData.value);
		console.log("------");
		if (customForm.value) {
			customForm.value.validate().then((res : any) => {
				console.log('success', res);
                let oPersonInfo = getCurrentMemberInfo();
                let params = { ...formData.value, ...oPersonInfo };
                createMedicinePlan(params).then(res=> {
                    if(res.success) {
				        uni.showToast({ title: '保存成功' });
                    }
                }).catch(err => {
                    console.log(err)
                })
			}).catch((err : any) => {
				console.log('err', err)
			});
		} else {
			//出错了
		}
	};
	onLoad(() => {
		//{"type":"1"}
		console.log('HISTORY_INDEX【onLoad】：页面加载完成');
        // 选择药物
		uni.$on("selectDrug", (event) => {
			if (!event) {
				return
			}
			console.log("selectDrug CALLBACK", event);
			formData.value.medicineName = event.name;
			selectDrug = event;
		})
        // 用药频率
		uni.$on("drugFrequecyEvent", (obj) => {
			if (!obj) {
				return
			}
			console.log("drugFrequecyEvent CALLBACK", obj);
            Object.assign(formData.value, obj);
		})
        // 服药时间
		uni.$on('medicineTimeEvent', (event) => {
			if (!event) {
				return
			}
			console.log("medicineTimeEvent CALLBACK", event);
			formData.value.planTime = event;
		})
	})

	onShow(() => {
		console.log("HISTORY_INDEX【onShow】：页面重新可见");

		//检查token情况

	});

	onUnload(() => {
		uni.$off("selectDrug")
		uni.$off("drugFrequecyEvent")
		uni.$off("medicineTimeEvent")
		console.error('页面onUnload');
	})
</script>

<style scoped lang="scss">
	.medicine-duration {
		margin-bottom: 20rpx;
	}

	.tag-options {
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-start;
		margin-top: 20rpx;
	}

	uni-tag {
		margin: 10rpx;
	}

	.custom-input {
		margin-top: 20rpx;
	}

	input {
		padding: 16rpx;
		border: 1px solid #ccc;
		border-radius: 4px;
		width: 100%;
	}
</style>