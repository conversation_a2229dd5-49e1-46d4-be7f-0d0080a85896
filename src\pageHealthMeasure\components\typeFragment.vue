<template>
	<view class="flex-col">
		<view class="flex flex-col items-stretch bg-[#ffffff] rounded-[16rpx] m-[36rpx] px-[20rpx] py-[36rpx]">

			<view v-if="viewMode == 'day'" class="flex flex-col px-[20rpx]">
				<view class="flex flex-row">
					<text class="text-[#014777]">当前日期：</text>
					<text class="">{{queryDateFlag}}</text>
				</view>

				<!-- 根据type去本地字典查询返回要现实的参数值对应的名称 -->
				<!-- for循环读取展示keyName、keyValue和单位 -->
				<view  class="flex flex-col mt-[24rpx] bg-[#f0f0f0]">
					<view class="flex flex-row justify-between list-border p-[20rpx]" v-for="(item, index) in mockData"
						:key="index ">
						<text>{{ item.keyName }}</text>
						<text>{{ item.keyValue }}</text>
						<text>{{ item.unit }}</text>
					</view>
                    <!-- 空数据展示 -->
                    <u-empty v-if="!mockData.length" mode="list" text="无数据" iconSize="50" :customStyle="{'margin-bottom': '10rpx'}"></u-empty>
                </view>
				<!-- 仿钉钉打卡日历组件 -->
				<lunc-calendar ref="calendar" :weekend="false" :showShrink="true" shrinkState="week"
					:signList="calendarDots" @dayChange="dayChange">
				</lunc-calendar>
			</view>

			<!-- -if="props.viewMode == 'month'"-->
			<view v-else-if="viewMode == 'month'" class="flex flex-col">
				<!-- monthTagList -->
				<up-grid :border="false" col="6" gap="12rpx" align="center">
					<up-grid-item v-for="(item,index) in monthTagList" :key="index">
						<uni-tag class="my-[12rpx]" :text="item.text" :disabled="item.disable" :inverted="!item.checked"
							type="primary" @click="radioClick(index)" />
					</up-grid-item>
				</up-grid>
                <view class="flex flex-col mt-[8rpx] rounded-[16rpx] bg-[#fff]"  v-for="(item, index) in MonthBpList">
                    <view class="flex flex-row justify-between list-border p-[24rpx]"
                        :key="index "> 
                        <view class="flex flex-col">
                            <text class="font-bold text-xl">{{ item.sbp || "-" }}/{{ item.dbp || "-" }}</text>
                            <text class="text-xs text-[#999]">mmHg</text>
                            </view>
                        <view class="flex flex-col">
                            <text class="font-bold text-xl">{{ item.pulse || "-" }}</text>
                            <text class="text-xs text-[#999]">bpm</text>
                        </view>
                        <text>{{ item.measureTime }}</text>
                    </view>
                    <view v-if="index!==MonthBpList.length-1" class="flex flex-col mx-[24rpx] bg-[#333]" style="height: 2.5rpx;"></view>
                </view>

                <!-- 血氧 -->
                <view class="flex flex-col mt-[8rpx] rounded-[16rpx] bg-[#fff]"  v-for="(item, index) in MonthSpozList">
                    <view class="flex flex-row justify-between list-border p-[24rpx]"
                        :key="index "> 
                        <view class="flex flex-col">
                            <text class="font-bold text-xl">{{ item.spoz || "-" }}</text>
                            <text class="text-xs text-[#999]">%SpO2</text>
                            </view>
                        <view class="flex flex-col">
                            <text class="font-bold text-xl">{{ item.hr || "-" }}</text>
                            <text class="text-xs text-[#999]">bpm</text>
                        </view>
                        <text>{{ item.measureTime }}</text>
                    </view>
                    <view v-if="index!==MonthSpozList.length-1" class="flex flex-col mx-[24rpx] bg-[#333]" style="height: 2.5rpx;"></view>
                </view>

                <!-- 体温 -->
                <view class="flex flex-row justify-between list-border p-[24rpx]" v-for="(item, index) in MonthTempList"
                    :key="index ">
                    <text>{{ item.measureTime }}</text>
                    <text>{{ item.temp || "-" }}℃</text>
                    <text>{{ oTempLevelName[item.tempLevel] || "-" }}</text>
                </view>
                <!-- 空数据 -->
                <u-empty v-if="!MonthBpList.length && !MonthTempList.length && !MonthSpozList.length" mode="list" text="无数据"
                    marginTop="30"></u-empty>
			
		    </view>
		<view v-else class="flex flex-col justify-center items-center">
			<text class="text-xl text-[#999]">未知的监测数据类型</text>
		</view>
	</view>

	<view class="flex items-stretch bg-[#ffffff] rounded-[16rpx] m-[36rpx] px-[20rpx] py-[36rpx]">
		<uni-icons type="notification-filled" size="36" color="#333"></uni-icons>

		<view class="flex-1 self-center pl-[8rpx]">
			<view class="text-lg text-[#333] text-left">闹钟设置</view>
		</view>

		<uni-icons type="right" size="32" color="#014777"></uni-icons>
	</view>

	<view class="flex items-stretch bg-[#ffffff] rounded-[16rpx] m-[36rpx] px-[20rpx] py-[36rpx]">
		<uni-icons type="notification-filled" size="36" color="#333"></uni-icons>

		<view class="flex-1 self-center pl-[8rpx]">
			<view class="text-lg text-[#333] text-left">添加服药记录</view>
		</view>

		<uni-icons type="right" size="32" color="#014777"></uni-icons>
	</view>

	<view class="flex items-stretch bg-[#ffffff] rounded-[16rpx] m-[36rpx] px-[20rpx] py-[36rpx]">
		<uni-icons type="flag" size="36" color="#333"></uni-icons>

		<view class="flex-1 self-center pl-[8rpx]">
			<view class="text-lg text-[#333] text-left">设置目标</view>
		</view>

		<uni-icons type="right" size="32" color="#014777"></uni-icons>
	</view>

	<view class="flex flex-1 justify-center text-white bg-[#0052A8] rounded-[16rpx] m-[36rpx] px-[20rpx] py-[36rpx]"
		@click="gotoManualEntry()">
		手动输入
	</view>

	<view class="flex flex-col items-stretch bg-[#ffffff] rounded-[16rpx] m-[36rpx] px-[20rpx] py-[36rpx]">
		<view class="flex flex-1 justify-center items-stretch self-center text-color-[#333] py-[20rpx]">
			提示
		</view>

		<view class="flex flex-1 justify-center items-stretch self-center text-color-[#999]">
			设备未连接，请先连接设备。
		</view>

		<view class="flex justify-center px-[20rpx] py-[36rpx]">
			<!-- <view class="text-green">
					取消
				</view> -->
			<view class="text-color-[#0052A8]">
				连接设备
			</view>
		</view>
	</view>

	</view>
</template>

<script setup lang="ts">
	import { ref, reactive, computed, watch, nextTick, onMounted } from 'vue';
	import type { IHealthObsResp } from '@/models/healthMeasure'
	import { onLoad, onShow, onUnload } from "@dcloudio/uni-app";
	import type { CommonTag } from '@/models/common'
	import moment from 'moment';
	import {
		getMonthTagList,
		fillKeyNameByTypeData
	} from "@/model_static/history";

	import {
		getLatsRecordByType,
		getDateMeasureRecord,
		getMonthMeasureRecord

	} from '@/common/api/measure'
	// import { userInfo } from 'os';
	let userInfo : any = null;
	const selectMenberType = ref<{ personName : string, personId : string | number }>();
	const isOnRequesting = ref<boolean>(false);
	const props = withDefaults(
		defineProps<{
			viewMode : string;
			dataType : string;
		}>(),
		{
			//没有默认值就是必填项
		}
	);


	const queryDateFlag = ref<string>("");
	const startOfMonth = ref<string>("");
	const endOfMonth = ref<string>("");
	const monthTagList = ref<CommonTag[]>(getMonthTagList());
	const radioClick = (myIndex : number) => {
		console.log('radioClick', myIndex);
		monthTagList.value.forEach((item, index) => {
			item.checked = index === myIndex;
		});
        let year = moment().year();
        startOfMonth.value = moment([year, myIndex]).startOf('month').format('YYYY-MM-DD');
        endOfMonth.value = moment([year, myIndex]).endOf('month').format('YYYY-MM-DD');
        requestDataWithMonth()
        // startOfMonth.value = moment().startOf('month').format('YYYY-MM-DD');
		// endOfMonth.value = moment().endOf('month').format('YYYY-MM-DD');
	};
	const mockData = ref<{ keyName : string, keyValue : string, unit : string }[]>([]);

	// 在响应式数据中使用类型
	const bpList = ref<any[]>([]);
	const tempList = ref<any[]>([]);
    const spozList = ref<any[]>([]);

	const MonthBpList = ref<any[]>([]);
	const MonthTempList = ref<any[]>([]);
    const MonthSpozList = ref<any[]>([]);


	const calendarDots = ref<{ date : string, title : string }[]>([
		{ date: "2024-12-25", title: "" },
		{ date: "2024-12-19", title: "" },
		{ date: "2024-12-22", title: "" },
		{ date: "2024-12-11", title: "" },
		{ date: "2024-12-23", title: "" }
	]);
    // 体温分级：1=正常；2=低热；3=中度发热；4=高度发热；5=超高热；
    const oTempLevelName = ref({
        1: '正常',
        2: '低热',
        3: '中度发热',
        4: '高度发热',
        5: '超高热',
    })

	const dayChange = (dayInfo : any) => { // 点击日期
		console.log("点击日期", JSON.parse(JSON.stringify(dayInfo)));
		queryDateFlag.value = dayInfo.date;
		requestDataWithDate();
	};
	const monthChange = (monthInfo : any) => { // 切换月份
		console.log("切换月份", JSON.parse(JSON.stringify(monthInfo)));
	};

	const clearDataBefoRequest = () => {
		bpList.value = [];
		tempList.value = [];
        spozList.value = [];

	};
	const requestDataWithDate = () => {
		clearDataBefoRequest();
		let bodyParms = {
			"endTime": queryDateFlag.value,
			"personId": selectMenberType.value?.personId,
			"signType": props.dataType,
			"startTime": queryDateFlag.value
		}
		console.log('bodyParms参数', bodyParms);
		getLatsRecordByType(bodyParms).then((res) => {
			//根据返回的数据格式，将数据分别存储到bpList/temlist
			console.log('接口返回数据:', res.data);
			if (res && res.data && res.data.bpList) {
				bpList.value = res.data.bpList;
			}
			if (res && res.data && res.data.tempList) {
				tempList.value = res.data.tempList
			}
            if (res && res.data && res.data.spozList) {
				spozList.value = res.data.spozList
			}
            
			//更新页面绑定的数据
			updateMockData();
		}).catch((error) => {


			console.log('requestDataWithDate!!!');

		}).finally(() => {
			// 无论成功还是失败，最终都需要恢复为 false
			isOnRequesting.value = false;

		})


	};
	const updateMockData = () => {
		mockData.value = [];
		console.log("进入更新绑定页面的数据函数")
		//处理血压数据
		if (bpList.value.length > 0) {
			console.log("处理血压数据")
			const bp = bpList.value[0];
			mockData.value.push(
				{ keyName: '收缩压', keyValue: bp.sbp, unit: 'mmHg' },
				{ keyName: '舒张压', keyValue: bp.sbp, unit: 'mmHg' },
				{ keyName: '脉搏', keyValue: bp.pulse, unit: 'bpm' },
				{ keyName: '测量时间', keyValue: bp.measureTime, unit: '' },
			);
		}
		//处理体温数据
		if (tempList.value.length > 0) {
			const temp = tempList.value[0];
			mockData.value.push(
				{ keyName: '温度', keyValue: temp.temp, unit: '℃' },
				{ keyName: '测量时间', keyValue: temp.measureTime, unit: '' },
			);
		}

        if (spozList.value.length > 0) {
            const data = spozList.value[0];
            mockData.value.push(
				{ keyName: '血氧', keyValue: data.spoz, unit: '%SpO2' },
				{ keyName: '脉搏', keyValue: data.hr, unit: 'bpm' },
                { keyName: '测量时间', keyValue: data.measureTime, unit: '' },
			);
        }

	}
	const requestDataWithMonth = () => {
		clearDataBefoRequest();
		let bodyParms = {
			"endTime": endOfMonth.value,
			"personId": selectMenberType.value?.personId,
			"signType": props.dataType,
			"startTime": startOfMonth.value
		}
		getLatsRecordByType(bodyParms).then((res) => {
			console.log('requestDataWithMonth********');
			if (res && res.data && res.data.bpList) {
				MonthBpList.value = res.data.bpList;
			}
			if (res && res.data && res.data.tempList) {
				MonthTempList.value = res.data.tempList
			}
            if (res && res.data && res.data.spozList) {
				MonthSpozList.value = res.data.spozList
			}
            
			//更新页面绑定的数据
			//updateMockData();

		}).catch((error) => {


			console.log('requestDataWithMonth********');

		}).finally(() => {
            // 无论成功还是失败，最终都需要恢复为 false
			isOnRequesting.value = false;
		})


	};

	const gotoManualEntry = () => {
        let index: number = 0;
        // index  血压=0； 体温=1； 血糖=2；体脂=3
        if(props.dataType === '3') {
            // 体温
            index = 1 
        } else if(props.dataType === '6'){
            // 体重
            index = 3 
        } else if(props.dataType === '7'){
            // 血糖
            index = 2
        }
		uni.navigateTo({
			url: "/pageHealthMeasure/manualEntry" + "?params=" + index,
			animationType: "slide-in-right",
			animationDuration: 300,
		});

	};


	watch(
		() => props.viewMode,
		(value) => {
			console.log('props.viewMode~~', props.viewMode);
			if (props.viewMode == "day") {
				console.log('进入viewMode为0，day');
				queryDateFlag.value = moment().format('YYYY-MM-DD');
				console.log('queryDateFlag.value' + queryDateFlag.value);
				if (isOnRequesting.value) {
					uni.showToast({
						title: '正在加载数据',
						icon: 'none'
					})
					return
				}
				isOnRequesting.value = true;
				nextTick(() => {
					requestDataWithDate();
				})

			} else if (props.viewMode == "month") {
				console.log('进入viewMode为1，month');
				startOfMonth.value = moment().startOf('month').format('YYYY-MM-DD');
				endOfMonth.value = moment().endOf('month').format('YYYY-MM-DD');
                // 当前月份选中状态
                monthTagList.value[moment().month()].checked = true
				if (isOnRequesting.value) {
					uni.showToast({
						title: '正在加载数据',
						icon: 'none'
					})
					return
				}
				isOnRequesting.value = true;
				nextTick(() => {
					requestDataWithMonth();
				})
			}
		},
		{ deep: false, immediate: true }
	);
	watch(
		() => props.dataType,
		(value) => {
            if(value === undefined || value === null || value === '') return
			console.log('watch~props.dataType~~', props.dataType);

			if (isOnRequesting.value) {
				uni.showToast({
					title: '正在加载数据',
					icon: 'none'
				})
				return
			}
			isOnRequesting.value = true;

			nextTick(() => {
				props.viewMode == "day" && requestDataWithDate() 
                props.viewMode == "month" && requestDataWithMonth();
			})
		},
		// { deep: false, immediate: true }
	);
	onMounted(() => {
		console.log('onMounted');
	});
	onShow(() => {
		console.log('历史数据页面加载onShow完成')
		let lastUserInfo = uni.getStorageSync('lastSelectedUserInfo');
		userInfo = uni.getStorageSync('userInfo');
		if (!lastUserInfo || !lastUserInfo.personId) {
			if (!userInfo || !userInfo.personId) {
				uni.showModal({
					content: "用户获取失败，请重新登录",
					success: (res) => {
						if (res.confirm) {
							uni.reLaunch({
								url: '/pages/login'
							})
						}
					}
				})
				return
			} else {
				selectMenberType.value = { personId: userInfo.personId, personName: userInfo.personInfo.personName }
			}
		} else {
			selectMenberType.value = { personId: lastUserInfo.personId, personName: lastUserInfo.personName }
		}
	})
</script>

<style lang="scss">
	.u-page__tag-item {
		margin-right: 20px;
	}
</style>