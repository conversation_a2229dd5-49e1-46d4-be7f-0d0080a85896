<template>
	<view class="flex flex-col items-center p-[30rpx]">

		<uni-icons class=" mt-[60rpx]" custom-prefix="iconfont" type="icon-wancheng" size="120" color="#19c3ba"></uni-icons>
		<text class="text-[#999] mt-[20rpx]">填写时间：{{ params.time }}</text>

		<view class="flex flex-col bg-white rounded-[20rpx] p-[30rpx] mt-[30rpx] mb-[30rpx]">
			<view class="flex flex-row justify-between items-center">
				<view class="text-[#333] text-lg">睡眠质量等级</view>
				<text class="text-[#E99D42]">{{params.levelName}}</text>
			</view>
			<up-line color="#333" margin="20px 0"></up-line>
			<view class="flex flex-row justify-between items-center">
				<view class="text-[#333] text-lg">得分</view>
				<text class="text-[#333]">{{params.score}}分</text>
			</view>
			<up-line color="#333" margin="20px 0"></up-line>
			<view class="text-[#999] text-lg">评分规则：</view>

			<text class="text-[#999] mt-[8rpx]">0～5分，提示睡眠质量很好；</text>
			<text class="text-[#999] mt-[8rpx]">6～10分，提示睡眠质量还行；</text>
			<text class="text-[#999] mt-[8rpx]">11～15分，提示睡眠质量一般；</text>
			<text class="text-[#999] mt-[8rpx]">16～21分，提示睡眠质量很差。</text>
			<text class="text-[#999] mt-[12rpx]">本次测量的数据和结果仅供参考，不作为医疗诊断依据</text>
		</view>

		<button class="my-2" type="primary" style="background-color: #2B97E5; color: white;" @click="onRetest">重新评测</button>
	</view>
</template>

<script setup lang="ts">
	import { ref, onMounted } from 'vue';
	import { onLoad, onShow, onUnload } from "@dcloudio/uni-app";

	const params = ref<any>();

	const onRetest = () => {

		uni.redirectTo({
			url: '/pageSheep/sheepTest',
			animationType: "slide-in-right",
			animationDuration: 300,
		})
	};

	onLoad((option) => {
		console.log('sheepTest【onLoad】：页面加载完成');

		params.value = JSON.parse(decodeURIComponent(option.params));

		console.log(params.value);

	})

	onMounted(() => {


	});

	onShow(() => {
		console.log("sheepTestResult【onShow】：页面重新可见");

		//检查token情况

	});

	onUnload(() => {
		console.error('页面onUnload');
	})
</script>

<style scoped lang="scss">

</style>