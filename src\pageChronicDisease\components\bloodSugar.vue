<template>
	<view class="flex flex-col items-stretch m-[28rpx] px-[20rpx] py-[28rpx]">

		<view class="bg-white rounded-[16rpx] m-[15rpx] p-[30rpx]">
			<view class="flex items-center justify-between">
				<text class="text-base text-[#333]">血糖</text>
			</view>

			<view class="charts-box">
				<qiun-data-charts type="area" :opts="chartsOpts" :chartData="chartData" />
			</view>
		</view>

		<view class="grid grid-cols-2">
			<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[24rpx] py-[24rpx]"
				@click="gotoManualEntry(1)">
				<text class="font-bold">手动输入</text>
			</view>

			<!-- @click="gotoMeasure" -->
			<view class="flex flex-col items-center bg-[#ddd] rounded-[16rpx] m-[24rpx] py-[24rpx]">
				<text class="font-bold">测量血糖</text>
			</view>
		</view>

		<view class="pt-[36rpx] pb-[12rpx] font-bold">血糖异常记录</view>
		<!-- <view class="flex flex-col bg-[#ffffff] rounded-[16rpx] mt-[8rpx] px-[20rpx] py-[36rpx]">
			<up-grid :border="false" col="3" gap="12rpx" align="center">
				<up-grid-item v-for="(item,index) in actionList" :key="index">
					<view class="flex flex-col flex-1 pl-[20rpx]">
						<text class="text-lg">轻度</text>
						<view class="text-sm pt-[8rpx]">
							0次
						</view>
					</view>
				</up-grid-item>
			</up-grid> 
		</view> -->
		<view class="grid grid-cols-3">
			<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[12rpx] py-[20rpx]">
				<text class="font-bold text-[#347caf]">偏低</text>
				<text class="pt-[12rpx]">{{ respInfo.record.stateLow }}次</text>
			</view>

			<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[12rpx] py-[20rpx]">
				<text class="font-bold text-[#333]">偏高</text>
				<text class="pt-[12rpx]">{{ respInfo.record.stateHigh }}次</text>
			</view>
			<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[12rpx] py-[20rpx]">
				<text class="font-bold text-[#81b337]">正常</text>
				<text class="pt-[12rpx]">{{ respInfo.record.stateNormal }}次</text>
			</view>
		</view>

		<view class="pt-[36rpx] pb-[12rpx] font-bold">血糖记录</view>
		<view class="flex flex-col">
			<view class="flex flex-col mt-[20rpx] rounded-[16rpx] bg-[#fff]"
				v-for="(item, index) in respInfo.recordList" key="index ">
				<view class="flex flex-row justify-between list-border p-[24rpx]">
					<text>{{ item.date }}</text>
					<text>单位：mmol/L</text>
				</view>
				<view class="flex flex-col mx-[24rpx] bg-[#333]" style="height: 2.5rpx;" />
				<!-- justify-between -->
				<view class="flex flex-row justify-around list-border p-[24rpx]">
					<view class="flex flex-col items-center py-[12rpx]">
						<text class="font-bold text-2xl">{{ item.empty || '-' }}</text>
						<text class="pt-[12rpx] text-[#333]">空腹</text>
					</view>
					<view class="flex flex-col items-center py-[12rpx]">
						<text class="font-bold text-2xl">{{ item.after || '-' }}</text>
						<text class="pt-[12rpx] text-[#333]">餐后2小时</text>
					</view>
				</view>
			</view>
		</view>

	</view>
</template>

<script setup lang="ts">
	import { onLoad, onShow, onReady } from "@dcloudio/uni-app";
	import { ref, reactive, computed, watch, onMounted } from 'vue';
	import { getBloodPressureOptions, getPulseValueOptions } from '@/model_static/bloodPresure'
	import type { IHealthObsResp } from '@/models/healthMeasure'
	import type { CommonTag } from '@/models/common'
	import {
		getMonthTagList,
		fillKeyNameByTypeData
	} from "@/model_static/history";

	import {
		getDateMeasureRecord,
		getMonthMeasureRecord
	} from '@/common/api/measure'
	import { nextTick } from 'process';

	/* const props = withDefaults(
		defineProps<{
			viewMode : string;
			dataType : string;
		}>(),
		{
			//没有默认值就是必填项
		}
	); */

	interface MySubmitForm {
		chart : object
		record : {
			stateNormal : number
			stateLow : number
			stateHigh : number
		},
		recordList : {
			date : string
			empty : string
			after : string
		}[]
	}
	const respInfo = ref<MySubmitForm>(
		{
			chart: {},
			record: {
				stateNormal: 0,
				stateLow: 0,
				stateHigh: 0
			},
			recordList: [{
				date: "2024-12-06",
				empty: "4.0",
				after: "5.0"
			}, {
				date: "2024-12-07",
				empty: "4.5",
				after: "5.0"
			}, {
				date: "2024-12-08",
				empty: "5.0",
				after: "5.5"
			}]
		})

	//早上和晚上共用opt
	const chartsOpts = ref<any>({
		color: ['#188cf7', '#93c978'],
		yAxis: {
			/* data: [{ min: 0, max: 48 }],
			splitNumber: 6 */
		},
		padding: [10, 10, 0, 6],
		legend: { show: true },
		extra: {
			area: {
				type: "curve",
				opacity: 0.2,
				addLine: true,
				width: 2,
				gradient: true,
				activeType: "hollow"
			}
		},
	});

	const chartData = ref();

	const updateChart = () => {
		console.log("updateMornig: ");
		chartData.value = {
			"categories": ["12-06", "12-07", "12-08", "12:09", "12-10", "12-11"],
			"series": [{
				"name": "空腹",
				"data": [4.5, 5.0, 4.5, 5.0, 5.0, 5.0]
			}, {
				"name": "餐后2小时",
				"data": [5.5, 5.5, 5.5, 6.0, 6.0, 6.5]
			}]
		};
	}

	const gotoManualEntry = (index :number) => {
		// 血压=0； 体温=1； 血糖=2；体脂=3
		console.log("gotoManualEntry: ",index);
		uni.navigateTo({
			url: "/pageHealthMeasure/manualEntry" + "?params=" + index
		})
	}

	const gotoMeasure = () => {
		console.log("gotoMeasure: ");

	}

	const gotoPlan = () => {
		console.log("gotoPlan: ");

	}

	/* watch(
		() => props.viewMode,
		(value) => {
			console.log('props.viewMode', props.viewMode);

		},
		{ deep: false, immediate: true }
	); */
	onMounted(() => {
		// 组件能被调用必须是组件的节点已经被渲染到页面上
		setTimeout(() => {
			updateChart();
		}, 300);
	})
</script>

<style lang="scss">
	.u-page__tag-item {
		margin-right: 20px;
	}

	.charts-box {
		width: 100%;
		height: 500rpx;
	}
</style>