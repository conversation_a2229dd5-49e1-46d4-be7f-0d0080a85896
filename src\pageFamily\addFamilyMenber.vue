<template>
	<view class="font">
		<view class="call_top">
			<view class="form_data">
				<!-- <view class="flex alignCenter">
					<input class="inp" :disabled="isDisabled" placeholder="请输入手机号码" maxlengtn="11" border="surround" v-model="phone" />
				</view> -->
				<view class="example">
					<!-- 自定义表单校验 -->
					<uni-forms ref="customForm" :rules="customRules" label-width="80px" :modelValue="customFormData">
						<uni-forms-item label="昵称" required name="personName">
							<div class="input-container">
								<input class="inp" style="flex:1;" v-model="customFormData.personName"
									placeholder="请输入昵称" />
								<button v-if="customFormData.personName" class="close-icon"
									@click="clearField('personName')">×</button>
								<!-- <uni-easyinput ref="nameInput" v-model="customFormData.name" placeholder="请输入姓名"/> -->
							</div>
						</uni-forms-item>
						<uni-forms-item label="真实姓名" name="realName">
							<div class="input-container">
								<input class="inp" v-model="customFormData.realName" placeholder="请输入真实姓名"
									border="surround" />
								<button v-if="customFormData.realName" class="close-icon"
									@click="clearField('realName')">×</button>
							</div>
						</uni-forms-item>

					</uni-forms>

				</view>
				<!-- <button class="btn addBtn" size="small" text="添加" :disabled="isDisabled" @tap="addPhone">添加</button> -->
				<button type="primary" style="height: 38px;line-height: 38px;width: 80%;background-color: #2B97E5;"
					@click="addFamilyMenber()">添加</button>

			</view>
			<view class="form_data form_dataT rounded-[35rpx] mt-5">
				<view class="flex flex_s borderBottom paddingB paddingT justify-between"
					v-for="(item,index) in callInfo" :key="index">
					<view class="flex-1">{{item.personName}}</view>
					<button plain class="btn del" v-if="item.personName !== currentPersonInfo.personName"  style="border: 1px solid #2B97E5; color:#2B97E5 ; width:160rpx; font-size: 28rpx"
						text="删除" size="small" @tap="delPhone(item.personId, item.personName)">删除</button>
				</view>
				<!-- <view class="textareaa_">
					<textarea class="textarea_" :disabled="isDisabled" v-model="callInfo.text" maxlength="60"
						height="240rpx" :count="true" placeholder="请输入内容" confirmType="done"></textarea>
				</view> -->
			</view>
		</view>
	</view>
</template>

<script setup lang='ts'>
	import {
		createPerson,
		getUserFamilyMembers,
		deletePerson
	} from '@/common/api/family'
	import {
		ref,
		onMounted
	} from 'vue';
    

    const currentPersonInfo = uni.getStorageSync('userInfo')?.personInfo || {};
    console.log(currentPersonInfo)

	const phone = ref < string > ('');
	const customForm = ref();
	interface RelationShip {
		text: string; // 关系的文本描述
		value: number; // 关系的值
	}

	let userInfo = null;
	// 自定义表单数据
	const customFormData = ref < {
		personName: string;
		realName: string;
	} > ({
		personName: '',
		realName: '',
	});
	// 多选数据源
	const relationShips = [{
		text: '户主',
		value: 0
	}, {
		text: '配偶',
		value: 1
	}, {
		text: '子女',
		value: 2
	}, {
		text: '父亲',
		value: 3
	}, {
		text: '母亲',
		value: 4
	}, {
		text: '岳父',
		value: 5
	}, {
		text: '岳母',
		value: 6
	}, {
		text: '其他',
		value: 7
	}];
	const max = 10; // 最多10个联系人
	const callInfo = ref < Array < {
		personId: string;personName: string;
	} >> ([]);



	// 自定义表单校验规则
	const customRules = {
		name: {
			rules: [{
				errorMessage: '姓名不能为空'
			}]
		},
		age: {
			rules: [{
				errorMessage: '年龄不能为空'
			}]
		},
		idNumber: {
			rules: [{
				errorMessage: '证件号不能为空'
			}]
		},
		phone: {
			rules: [{
				required: true,
				errorMessage: '手机号不能为空'
			}, {
				pattern: /^1[3-9]\d{9}$/, // 匹配中国大陆的手机号码格式
				errorMessage: '域名格式错误'
			}]
		},
		relationType: { // 新增关系类型的验证规则
			rules: [{
				errorMessage: '关系类型不能为空'
			}]
		}
	};
	
	const delPhone = (id: string, personName: string) => {
        console.log("!!!!!!"+id);
        uni.showModal({
            title: '提示：',
            content: `请确认是否删除成员'${personName}'？`,
            success: function(res) {
                console.log(res);
                if(res.confirm) {
                    deletePerson({personId: id}).then(res=>{
                        console.log('删除成功', res);
                        uni.showToast({
                            title:'删除成功',
                            icon:'success'
                        })
                        requestUserFamilyMembers();
                    }).catch(error=> {
                        console.error('删除失败', error);
                        uni.showToast({
                            title:'删除失败',
                            icon:'none'
                        })
                    })
                }
            },
        })
	}
	const clearField = (field: string) => {
		console.log("清空指定的字段");
		customFormData.value[field] = '';
	};
    // 重置表单内容和清除校验
    const resetFormData = () => {
        Object.keys(customFormData.value).map(field => {
            customFormData.value[field] = undefined;
            clearField(field)
        })
    }
	const typeChanged = (e) => {
		console.log("typeChanged", e);
		customFormData.value.relationShips = e.detail.value;
	}
	const addFamilyMenber = () => {
		console.log("~~~~~~~~~~~~~~~" + JSON.stringify(customFormData.value));

		let arg = Object.assign(customFormData.value, {
			createUserId: userInfo.userId,
			personId: userInfo.personId
		});
		// 调用 createPerson 接口，传递 customForm.value 作为参数
		createPerson(arg)
			.then(response => {
				// 处理成功响应
				console.log('创建成功', response);
				uni.showToast({
					title: '创建成功',
					icon: 'success'
				});
                resetFormData()
				requestUserFamilyMembers();

			})
			.catch(error => {
				// 处理接口调用失败的情况
				console.error('创建失败', error);
				uni.showToast({
					title: '创建失败',
					icon: 'none'
				});
			});


	};

	const requestUserFamilyMembers = () => {
		console.log("~~~~~~~~~~userInfo.userId"+userInfo.userId)
		getUserFamilyMembers({
				userId: userInfo.userId
			})
			.then(res => {
				// 处理成功响应
				callInfo.value = res.data;

			})
			.catch(error => {
				// 处理接口调用失败的情况
				console.error('请求失败', error);
				uni.showToast({
					title: '请求失败',
					icon: 'none'
				});
			});
	};


	onMounted(() => {
		userInfo = uni.getStorageSync("userInfo");
		if (!userInfo || !userInfo.personId || !userInfo.userId) {
			uni.showModal({
				content: "用户信息获取失败，请重新登录",
				success: (res) => {
					if (res.confirm) {
						uni.reLaunch({
							url: '/pages/login',
						})
					}
				}
			})
			return
		}

		setTimeout(() => {
			requestUserFamilyMembers();
		}, 300);

	})
</script>

<style lang="scss" scoped>
	.call_top {
		height: 300rpx;
		background-color: #237FC2;
		background-repeat: no-repeat;
		background-size: 100% 100%;
		padding: 0 32rpx;
		padding-top: 60rpx;

		.title {
			padding-top: 45rpx;
			padding-bottom: 20px;
			font-size: 44rpx;
			color: #fff;

			.red {
				color: #DE4F4F;
				padding-right: 10rpx;
			}
		}


		.form_data {
			padding: 48rpx 32rpx 48rpx 32rpx;
			background-color: #fff;
			border-radius: 15rpx;

			.btn {
				width: 320rpx;
				margin-left: 16rpx;
				height: 74rpx;
				line-height: 74rpx;
				background-color: #4187f8;
				color: #fff;
				border-radius: 10rpx;
			}

			.del {
				background: none;
				border: 1px solid #139d7a;
				color: #139d7a;
				height: 60rpx;
				line-height: 60rpx;
				border-radius: 5rpx;
			}

			.del:hover {
				background-color: #0c614b;
			}

		}

		.inp {
			border: 1px solid #dadbde;
			padding: 12rpx 18rpx;
			width: 78%;
		}

		.form_dataT {
			padding-top: 10rpx;
			margin-top: 24rpx;

			.inp {
				border: none;
			}

			.textareaa_ {
				margin-top: 20rpx;
				width: 100%;

				.textarea_ {
					width: 100%;
					box-sizing: border-box;
					background-color: #F6F6F6;
					border: none;
					color: #666;
					padding: 20rpx;
				}
			}
		}

	}

	.flex {
		display: flex;
	}

	.alignCenter {
		align-items: center;
	}

	.flex_s {
		justify-content: space-between;
	}

	.borderBottom {
		border-bottom: 1px solid #E4E4E4;
	}

	.paddingT {
		padding-top: 14rpx;
	}

	.paddingB {
		padding-bottom: 14rpx;
	}

	.addBtn {
		width: 120rpx;
		height: 74rpx;
		font-size: 24rpx;
		text-align: center;
		line-height: 74rpx;
		border-radius: 10rpx;
	}

	.save {
		width: 100%;
		position: fixed;
		bottom: 0;
		left: 0;
		background-color: #3779E4;
		color: #fff;
	}

	.input-container {
		position: relative;
		/* 使容器相对定位 */
	}

	.inp {
		padding-right: 30px;
		/* 为了给关闭按钮留出空间 */
	}

	.close-icon {
		width: 32px;
		height: 36px;
		line-height: 36px;
		position: absolute;
		z-index: 9999;
		right: 34px;
		/* 距离右边的距离 */
		top: 50%;
		/* 垂直居中 */
		transform: translateY(-50%);
		/* 使按钮垂直居中 */
		background: none;
		border: none;
		color: red;
		/* 根据需要设置颜色 */
		cursor: pointer;
		font-size: 18px;
		/* 根据需要设置大小 */
	}

	.close-icon:hover {
		color: darkred;
		/* 鼠标悬停时颜色 */
	}

	button::after {
		border: none
	}
</style>