import request from '@/common/request'
import type{ ILogin, ILoginResp } from '@/models/login'

/**
 * 登录
 * @param data 请求体参数 /monitor/need/reptile/pageList
 */
export const reptileList = (data : any) => {
	return request({
		method: 'POST',
		url: '/monitor/need/reptile/pageList',
		body: data,
		hideLoading: false,
		custom: {
			ignoreRespInterceptors: true //忽略本次请求的响应拦截
		}
	})
}

/**
 * 查询成员当天的服药任务
 * @param data 请求体参数 /monitor/need/medicineBox/queryMedicineTask
 */
export const queryMedicineTask = (data : any) => {
	return request({
		method: 'POST',
		url: '/monitor/need/medicineBox/queryMedicineTask',
		body: data,
		hideLoading: false,
		custom: {
			// ignoreRespInterceptors: true //忽略本次请求的响应拦截
		}
	})
}
 
/**
 * 创建服药计划
 * @param data /monitor/need/medicineBox/createMedicinePlan
 */
export const createMedicinePlan = (data : any) => {
	return request({
		method: 'POST',
		url: '/monitor/need/medicineBox/createMedicinePlan',
		body: data,
		hideLoading: false,
		custom: {
			// ignoreRespInterceptors: true //忽略本次请求的响应拦截
		}
	})
}
/**
 * 停止服药计划
 * @param data /monitor/need/medicineBox/stopMedicinePlan
 */
export const stopMedicinePlan = (data : any) => {
	return request({
		method: 'POST',
		url: '/monitor/need/medicineBox/stopMedicinePlan',
		body: data,
		hideLoading: false,
		custom: {
			// ignoreRespInterceptors: true //忽略本次请求的响应拦截
		},
        headers: {
            'content-type': 'application/x-www-form-urlencoded',
        },
	})
}