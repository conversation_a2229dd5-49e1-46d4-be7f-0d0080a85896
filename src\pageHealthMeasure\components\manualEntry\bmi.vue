<template>
	<view class="flex flex-col items-stretch m-[28rpx] px-[20rpx] py-[28rpx]">

		<view class="pb-[12rpx] font-bold">必填</view>
		<up-form ref="uFormRef" labelPosition="left" :rules="rules" :model="submitForm">
			<view class="flex items-center bg-[#ffffff] rounded-[16rpx] mt-[8rpx] px-[20rpx] py-[36rpx]">
				<view class="text-lg">时间</view>
				<view class="flex-1 pl-[36rpx]">
					<up-form-item label="" prop="datetime" labelWidth="0rpx">
						<uni-datetime-picker type="datetime" v-model="submitForm.datetime" />
					</up-form-item>
				</view>

				<uni-icons type="right" size="32" color="#014777"></uni-icons>
			</view>

			<view class="flex items-center bg-[#ffffff] rounded-[16rpx] mt-[36rpx] px-[20rpx] py-[36rpx]">
				<view class="text-lg">体重</view>
				<view class="flex-1 pl-[36rpx]">
					<up-form-item label="" prop="weight" labelWidth="0rpx">
						<view style="width: 100%;" @click="showWeightPicker=true">
							<up-input v-model="submitForm.weight" placeholder="输入体重" border="none" readonly />
						</view>
					</up-form-item>
				</view>

				<view class="flex flex-row items-center">
					<view class="px-[12rpx] text-[#999]">Kg</view>
					<uni-icons type="right" size="32" color="#014777"></uni-icons>
				</view>
			</view>
			<!-- :defaultIndex="[heightDefIndex]"	 -->
			<view class="flex items-center bg-[#ffffff] rounded-[16rpx] mt-[36rpx] px-[20rpx] py-[36rpx]">
				<view class="text-lg">身高</view>
				<view class="flex-1 pl-[36rpx]">
					<up-form-item label="" prop="height" labelWidth="0rpx">
						<view style="width: 100%;" @click="showHeightPicker=true">
							<up-input v-model="submitForm.height" placeholder="输入身高" border="none" readonly />
						</view>
						<up-picker :show="showHeightPicker" :columns="heightOptions" @cancel="showHeightPicker=false"
						 @confirm="changeConfirmHeight"></up-picker>
					</up-form-item>
				</view>
			
				<view class="flex flex-row items-center">
					<view class="px-[12rpx] text-[#999]">Cm</view>
					<uni-icons type="right" size="32" color="#014777"></uni-icons>
				</view>
			</view>
		</up-form>

		<view class="pt-[36rpx] pb-[12rpx] font-bold">非必填</view>
		<view class="flex items-center bg-[#ffffff] rounded-[16rpx] mt-[8rpx] px-[20rpx] py-[36rpx]">
			<view class="text-lg">体脂肪率</view>
			<view class="flex-1 pl-[36rpx]">
				<up-form-item label="" prop="bodyFat" labelWidth="0rpx">
					<view style="width: 100%;" @click="showBodyFatPicker=true">
						<up-input v-model="submitForm.bmi" placeholder="输入体脂肪率" border="none" readonly />
					</view>
				</up-form-item>
			</view>

			<view class="flex flex-row items-center">
				<view class="px-[12rpx] text-[#999]">%</view>
				<uni-icons type="right" size="32" color="#014777"></uni-icons>
			</view>
		</view>

		<view class="flex items-center bg-[#ffffff] rounded-[16rpx] mt-[36rpx] px-[20rpx] py-[36rpx]">
			<view class="text-lg">内脏脂肪水平</view>
			<view class="flex-1 pl-[36rpx]">
				<up-form-item label="" prop="visceral" labelWidth="0rpx">
					<view style="width: 100%;" @click="showVisceralPicker=true">
						<up-input v-model="submitForm.visceral" placeholder="输入内脏脂肪水平" border="none" readonly />
					</view>
				</up-form-item>
			</view>

			<view class="flex flex-row items-center">
				<view class="px-[12rpx] text-[#999]">级</view>
				<uni-icons type="right" size="32" color="#014777"></uni-icons>
			</view>
		</view>

		<view class="flex items-center bg-[#ffffff] rounded-[16rpx] mt-[36rpx] px-[20rpx] py-[36rpx]">
			<view class="text-lg">骨骼肌率</view>
			<view class="flex-1 pl-[36rpx]">
				<up-form-item label="" prop="visceral" labelWidth="0rpx">
					<view style="width: 100%;" @click="showVisceralPicker=true">
						<up-input v-model="submitForm.visceral" placeholder="输入骨骼肌率" border="none" readonly />
					</view>
				</up-form-item>
			</view>

			<view class="flex flex-row items-center">
				<view class="px-[12rpx] text-[#999]">%</view>
				<uni-icons type="right" size="32" color="#014777"></uni-icons>
			</view>
		</view>

		<view class="flex items-center bg-[#ffffff] rounded-[16rpx] mt-[36rpx] px-[20rpx] py-[36rpx]">
			<view class="text-lg">体年龄</view>
			<view class="flex-1 pl-[36rpx]">
				<up-form-item label="" prop="visceral" labelWidth="0rpx">
					<view style="width: 100%;" @click="showBodyAgePicker=true">
						<up-input v-model="submitForm.bodyAge" placeholder="输入体年龄" border="none" readonly />
					</view>
				</up-form-item>
			</view>

			<view class="flex flex-row items-center">
				<view class="px-[12rpx] text-[#999]">岁</view>
				<uni-icons type="right" size="32" color="#014777"></uni-icons>
			</view>
		</view>

		<!-- <view class="flex items-center bg-[#ffffff] rounded-[16rpx] mt-[36rpx] px-[20rpx] py-[36rpx]">
			<view class="text-lg">BMI</view>
			<view class="flex-1 pl-[36rpx]">
				<up-form-item label="" prop="visceral" labelWidth="0rpx">
					<view style="width: 100%;" @click="showBMIPicker=true">
						<up-input v-model="submitForm.bmi" placeholder="输入BMI" border="none" readonly />
					</view>
				</up-form-item>
			</view>

			<view class="flex flex-row items-center">
				<view class="px-[12rpx] text-[#999]"></view>
				<uni-icons type="right" size="32" color="#014777"></uni-icons>
			</view>
		</view> -->

		<view class="pt-[36rpx] pb-[12rpx] font-bold">测量时活动</view>
		<view class="flex flex-col bg-[#ffffff] rounded-[16rpx] mt-[8rpx] px-[20rpx] py-[36rpx]">
			<up-grid :border="false" col="4" gap="12rpx" align="center">
				<up-grid-item v-for="(item,index) in actionList" :key="index">
					<!-- <uni-tag class="my-[12rpx]" style="min-width: 48rpx;" :text="item.text" :disabled="item.disable"
						:inverted="!item.checked" type="primary" @click="radioClick(index)" /> -->
					<up-tag class="my-[12rpx]" style="min-width: 48rpx;" :text="item.text" shape="circle"
						:bgColor="item.checked ? '#0081ff' : '#ccc'" :borderColor="item.checked ? '#0081ff' : '#ccc'"
						@click="radioClick(index)"> </up-tag>
				</up-grid-item>
			</up-grid>
		</view>

		<view class="flex flex-1 justify-center text-white bg-[#0052A8] rounded-[16rpx] mt-[50rpx] px-[20rpx] py-[36rpx]"
			@click="doSubmit()">
			确定
		</view>

		<numeric-picker :show="showWeightPicker" dataType="float1" :range="[1,100]" @onCancel="showWeightPicker=false"
			@onConfirm="onConfirmWeight"></numeric-picker>

		<numeric-picker :show="showBodyFatPicker" dataType="float1" :range="[5.0,200.0]" @onCancel="showBodyFatPicker=false"
			@onConfirm="onConfirmBodyFat"></numeric-picker>

		<numeric-picker :show="showSkeletalPicker" dataType="integer" :range="[1,50]" @onCancel="showSkeletalPicker=false"
			@onConfirm="onConfirmSkeletal"></numeric-picker>

		<numeric-picker :show="showVisceralPicker" dataType="integer" :range="[20,80]" @onCancel="showVisceralPicker=false"
			@onConfirm="onConfirmVisceral"></numeric-picker>

		<numeric-picker :show="showBodyAgePicker" dataType="integer" :range="[5,150]" @onCancel="showBodyAgePicker=false"
			@onConfirm="onConfirmBodyAge"></numeric-picker>

		<numeric-picker :show="showBMIPicker" dataType="float1" :range="[5.0,100.0]" @onCancel="showBMIPicker=false"
			@onConfirm="onConfirmBMI"></numeric-picker>
	</view>
</template>

<script setup lang="ts">
	import moment from 'moment';
	import { onLoad, onShow, onReady } from "@dcloudio/uni-app";
	import { onMounted, ref, reactive, computed, watch } from 'vue';
	import type { UniFormRef } from '@/uni_modules/uview-plus/types'
	const uFormRef = ref<UniFormRef | null>(null)
	import {getHeightValueOptions } from '@/model_static/bloodPresure'
	import type { IHealthObsResp } from '@/models/healthMeasure'
	import type { CommonTag } from '@/models/common'
	import { createBmiRecord } from '@/common/api/measure';
	import {
		getMonthTagList,
		fillKeyNameByTypeData
	} from "@/model_static/history";

	import {
		getDateMeasureRecord,
		getMonthMeasureRecord
	} from '@/common/api/measure'

	const props = withDefaults(
		defineProps<{
			viewMode : string;
			dataType : string;
		}>(),
		{
			//没有默认值就是必填项
		}
	);

	interface MySubmitForm {
		weight : string
		height: string
		bodyFat : string
		visceral : string
		skeletal : string
		bodyAge : string
		bmi : string
		datetime : string
		action : string
	}
	const submitForm = ref<MySubmitForm>(
		{
			weight: "",
			height:"",
			bodyFat: "",
			visceral: "",
			skeletal: "",
			bodyAge: "",
			bmi: "",
			datetime: "",
			action: "无"//
		})

	const showWeightPicker = ref(false);
	const showHeightPicker = ref(false);
	const showBodyFatPicker = ref(false);
	const showVisceralPicker = ref(false);
	const showSkeletalPicker = ref(false);
	const showBodyAgePicker = ref(false);
	const showBMIPicker = ref(false);
	const heightOptions = reactive([getHeightValueOptions()]);
	const heightDefIndex = ref(158 - Number(heightOptions[0][0]));
// 监听身高和体重的变化
watch(
  () => [submitForm.value.height, submitForm.value.weight],
  () => {
    calculateBodyFat(); // 每当身高或体重变化时重新计算体脂肪率
  }
);

// 计算体脂肪率的方法
const calculateBodyFat = () => {
  const heightInM = submitForm.value.height / 100; // 将身高转换为米
  const weight = submitForm.value.weight;

  if (heightInM && weight) {
    const bmi = weight / (heightInM * heightInM); // 计算BMI
    const bodyFat = (1.20 * bmi) - 16.2; // 假设为男性体脂肪率（可根据性别调整）

    submitForm.value.bmi = bodyFat.toFixed(2); // 保留两位小数
  }
};
	const onConfirmWeight = (selValue : string) => {
		console.log("onConfirm", selValue);

		if (!selValue) {
			uni.showToast({
				title: "请选择有效的数值",
				icon: "none"
			})
			return
		}
		submitForm.value.weight = selValue;
		showWeightPicker.value = false;
	};
	const changeConfirmHeight = (e) => {
		console.log('change', e);
		submitForm.value.height = String(e.value[0]);
		showHeightPicker.value = false;
		hightDefIndex.value = e.indexs[0];
	};
	const onConfirmBodyFat = (selValue : string) => {
		console.log("onConfirmBodyFat", selValue);

		if (!selValue) {
			uni.showToast({
				title: "请选择有效的数值",
				icon: "none"
			})
			return
		}
		submitForm.value.bodyFat = selValue;
		showBodyFatPicker.value = false;
	};

	const onConfirmVisceral = (selValue : string) => {
		console.log("onConfirm", selValue);

		if (!selValue) {
			uni.showToast({
				title: "请选择有效的数值",
				icon: "none"
			})
			return
		}
		submitForm.value.visceral = selValue;
		showVisceralPicker.value = false;
	};

	const onConfirmSkeletal = (selValue : string) => {
		console.log("onConfirmSkeletal", selValue);

		if (!selValue) {
			uni.showToast({
				title: "请选择有效的数值",
				icon: "none"
			})
			return
		}
		submitForm.value.skeletal = selValue;
		showSkeletalPicker.value = false;
	};

	const onConfirmBodyAge = (selValue : string) => {
		console.log("onConfirm", selValue);

		if (!selValue) {
			uni.showToast({
				title: "请选择有效的数值",
				icon: "none"
			})
			return
		}
		submitForm.value.bodyAge = selValue;
		showBodyAgePicker.value = false;
	};

	const onConfirmBMI = (selValue : string) => {
		console.log("onConfirm", selValue);

		if (!selValue) {
			uni.showToast({
				title: "请选择有效的数值",
				icon: "none"
			})
			return
		}
		submitForm.value.bmi = selValue;
		showBMIPicker.value = false;
	};

	const actionList = ref<CommonTag[]>(
		[
			{
				text: '无',
				value: 0,
				checked: true,
				disable: false
			}, {
				text: '运动',
				value: 2,
				checked: false,
				disable: false
			}, {
				text: '吃饭',
				value: 3,
				checked: false,
				disable: false
			}, {
				text: '起床',
				value: 4,
				checked: false,
				disable: false
			}, {
				text: '睡前',
				value: 5,
				checked: false,
				disable: false
			}
		]
	);
	const rules = {
		'high': {
			type: 'string',
			required: true,
			message: '请输入高压值',
			trigger: ['blur'],
		},
		'low': {
			type: 'string',
			required: true,
			message: '请输入低压值',
			trigger: ['blur'],
		},
		'pulse': {
			type: 'string',
			required: true,
			message: '请输入脉搏',
			trigger: ['blur'],
		},
		'PulseRate': {
			type: 'string',
			required: true,
			message: '请输入脉率',
			trigger: ['blur'],
		},
		'datetime': {
			type: 'string',
			required: true,
			message: '请选择时间',
			trigger: ['blur', 'change'],
		}
	};
	const radioClick = (myIndex : number) => {
		console.log('radioClick', myIndex);
		submitForm.value.action = actionList.value[myIndex].text;

		actionList.value.forEach((item, index) => {
			item.checked = index === myIndex;
		});
	};

	const doSubmit = () => {
		console.log("submitForm: " + JSON.stringify(submitForm.value));
		if (!uFormRef.value) {
			return
		}

		uFormRef.value.validate().then((valid : boolean) => {

			console.log(valid);
			if (valid) {

				if (!submitForm.value.action) {
					uni.showToast({
						title: "请选择活动",
						icon: "none"
					})
					return
				}

				let bodyParams = {
					"measureTimeStr": moment(submitForm.value.datetime).format("YYYY-MM-DD HH:mm"),
					"personId": selectMenberType.value?.personId,
					"personName": selectMenberType.value?.personName,
					 "bmi": submitForm.value.bmi,
					  //"height": "", 
					  "others": submitForm.value.action,
					  "weight": submitForm.value.weight,
					  "height": submitForm.value.height,
				}
				createBmiRecord(bodyParams).then((res) => {
					uni.showToast({
						title: '提交成功',
						icon: 'none'
					})
				
					submitForm.value = {
			weight: "",
			height: "",
			//bodyFat: "",
			visceral: "",
			skeletal: "",
			bodyAge: "",
			bmi: "",
			datetime: "",
			action: "无"//
		};
				}).catch((error : any) => {
				
					console.log("error: " + JSON.stringify(error));
					// 处理验证错误  
					uni.showToast({
						title: "提交失败",
						icon: "none"
					})
				});
			} else {
				uni.showToast({
					title: "请规范完成表单录入",
					icon: "none"
				})
			}
		}).catch((error : any) => {

			console.log("error: " + JSON.stringify(error));
			// 处理验证错误  
			uni.showToast({
				title: "请规范完成表单录入",
				icon: "none"
			})
		});
	}

	watch(
		() => props.viewMode,
		(value) => {
			console.log('props.viewMode', props.viewMode);

		},
		{ deep: false, immediate: true }
	);
	const selectMenberType = ref<{ personName : string, personId : string | number }>();
	onMounted(() => {
		let lastUserInfo = uni.getStorageSync("lastSelectedUserInfo");
		let userInfo = uni.getStorageSync("userInfo");
	
		if (!lastUserInfo || !lastUserInfo.personId) {
			if (!userInfo || !userInfo.personId) {
				uni.showModal({
					content: "用户信息获取失败，请重新登录",
					success: (res) => {
						if (res.confirm) {
							uni.reLaunch({
								url: '/pages/login',
							})
						}
					}
				})
				return
			} else {
				selectMenberType.value = { personId: userInfo.personId, personName: userInfo.personInfo.personName }
			}
		} else {
			selectMenberType.value = { personId: lastUserInfo.personId, personName: lastUserInfo.personName }
		}
	
	});
</script>

<style lang="scss">
	.u-page__tag-item {
		margin-right: 20px;
	}
</style>