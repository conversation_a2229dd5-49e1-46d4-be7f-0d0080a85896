<template>
	<view class="p-[30rpx]">
		<view class="bg-white rounded-[20rpx] p-[30rpx] mt-[30rpx] mb-[30rpx]">

			<uni-forms ref="customForm" :modelValue="formData" :rules="customRules" :label-width="100"
				class="mt-[30rpx] px-[20rpx]">

				<uni-forms-item label="报告名称" required name="name">
					<input style="width: 400rpx;" v-model="formData.name" placeholder="如:血检报告" />
				</uni-forms-item>
				<uni-forms-item label="报告类型">
					<uni-data-picker v-model="formData.report" :localdata="typeData" popup-title="选择报告类型">
					</uni-data-picker>
				</uni-forms-item>
				<uni-forms-item label="检查时间">
					<uni-datetime-picker type="datetime" return-type="timestamp" v-model="formData.checkTime" />
				</uni-forms-item>

			</uni-forms>

		</view>

		<view class="bg-white rounded-[20rpx] p-[30rpx] mt-[30rpx] mb-[30rpx]">

			<view class="example-body">
				<uni-file-picker limit="9" title="选择图片" file-mediatype="image" :source-type="['album', 'camera']"></uni-file-picker>
			</view>
			<view class="example-body">
				<uni-file-picker limit="9" title="PDF" file-mediatype="all" :file-extname="['pdf']"></uni-file-picker>
			</view>

			<!-- #ifdef H5 || MP-WEIXIN -->
			<view class="example-body">
				<uni-file-picker limit="5" file-mediatype="all" title="最多选择5个文件"></uni-file-picker>
			</view>
			<!-- #endif -->
		</view>

		<view class="flex flex-row">
			您可上传近期的体检报告、血检报告、CT报告、
			动态心电分析报告等。
			<view class="flex flex-row items-center">
				查看图片示例图片
			</view>
		</view>

		<view class="flex flex-row">
			支持jpg、png格式，最多可上传5张，每张限 10M:PDF限传1份，大小不超过20M;请确保上 传的图片清晰 </view>

		<view>
			<button @click="submit()" size="default" class="text-[#fff] bg-[#2B97E5] rounded-[50rpx] mt-[50rpx]">
				提交
			</button>
		</view>

	</view>
</template>

<script setup lang="ts">
	import { ref } from 'vue';

	const typeData = [{
		text: "体检报告",
		value: "10001",
	}, {
		text: "检验报告",
		value: "10002",
	}, {
		text: "门诊病例",
		value: "10003",
	}];
	const relationData = [{
		text: "本人",
		value: "10001",
	}, {
		text: "配偶",
		value: "10002",
	}, {
		text: "父母",
		value: "10003",
	}, {
		text: "子女",
		value: "10004",
	}];
	// 校验表单数据
	const formData = ref({
		name: '',
		report: '',
		checkTime: '',
		phone: '',
		relation: '',
	});
	// 自定义表单校验规则
	const customRules = {
		name: {
			rules: [{
				required: true,
				errorMessage: '姓名不能为空'
			}]
		},
		phone: {
			rules: [{
				required: true,
				errorMessage: '手机号不能为空'
			}, {
				pattern: /^1[3-9]\d{9}$/, // 匹配中国大陆的手机号码格式
				errorMessage: '域名格式错误'
			}]
		},
	};
	// 创建一个 ref 对象，用于绑定组件的引用
	const customForm = ref();

	const submit = () => {
		console.log("------");
		if (customForm.value) {
			customForm.value.validate().then((res : any) => {
				console.log('success', res);
				uni.showToast({ title: '校验通过' })

			}).catch((err : any) => {
				console.log('err', err)
			});
		} else {
			//出错了
		}
	};
	/*  const handleSubmit = () => {
		console.log("------");
		if (valiForm.value) {
		valiForm.value.validate().then((res) => {
			console.log('表单数据信息：', res);
		}).catch((err) => {
			console.log('表单错误信息：', err);
		});
		}
	}; */
	/*onMounted(() => {
		if (formRef.value) {
		formRef.value.setRules(formRef);
		}
	}); */
</script>

<style scoped lang="scss">
	input {
		padding: 16rpx;
		border: 1px solid #ccc;
		border-radius: 4px;
		width: 100%;
	}
</style>