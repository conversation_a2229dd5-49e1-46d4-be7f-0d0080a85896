import request from '@/common/request'

/**
 * 创建家庭成员
 * @param data 请求体参数
 */
export const createPerson = (data : any) => {
	return request({
		method: 'POST',
		url: '/monitor/need/familyPerson/createPerson',
		body: data,
		hideLoading: false,
		/* custom: {
			ignoreRespInterceptors: true //忽略本次请求的响应拦截
		} */
	})
}

/**
 * 修改家庭成员信息
 * @param data 请求体参数
 */
export const editPerson = (data : any) => {
	return request({
		method: 'POST',
		url: '/monitor/need/familyPerson/editPerson',
		body: data,
		hideLoading: false,
		/* custom: {
			ignoreRespInterceptors: true //忽略本次请求的响应拦截
		} */
	})
}

/**
 * 获取用户家庭成员列表
 * @param data 请求体参数
 */
export const getUserFamilyMembers = (data : any) => {
	 // 从 data 中提取 userId 参数
	  const { userId } = data;
	  // 构建 URL 查询字符串
	  const url = `/monitor/need/familyPerson/getUserFamilyMembers?userId=${userId}`;
	return request({
		method: 'POST',
		url: url,
		body: null,
		hideLoading: false,
		/* custom: {
			ignoreRespInterceptors: true //忽略本次请求的响应拦截
		} */
	})
}

 
 /**
  * 删除家庭成员
  * @param data 请求体参数
  */
 export const deletePerson = (data : any) => {
 	 // 从 data 中提取 userId 参数
 	  const { personId } = data;
 	  // 构建 URL 查询字符串
 	  const url = `/monitor/need/familyPerson/deletePerson?personId=${personId}`;
 	return request({
 		method: 'POST',
 		url: url,
 		body: null,
 		hideLoading: false,
 		/* custom: {
 			ignoreRespInterceptors: true //忽略本次请求的响应拦截
 		} */
 	})
 }
 