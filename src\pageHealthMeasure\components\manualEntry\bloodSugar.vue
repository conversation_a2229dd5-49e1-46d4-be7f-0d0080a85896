<template>
	<view class="flex flex-col items-stretch m-[20rpx] px-[20rpx] py-[28rpx]">

		<view class="flex flex-col bg-[#ccc] rounded-[16rpx] px-[20rpx] py-[20rpx]">
			<up-grid :border="false" col="3" gap="12rpx" align="center">
				<up-grid-item v-for="(item,index) in actionList" :key="index">
					<!-- <uni-tag class="my-[12rpx]" style="min-width: 48rpx;" :text="item.text" :disabled="item.disable"
						:inverted="!item.checked" type="primary" @click="radioClick(index)" /> -->
					<!-- <up-b :text="item.text"
						:bgColor="item.checked ? '#014777' : '#ccc'" :borderColor="item.checked ? '#014777' : '#ccc'"
						@click="radioClick(index)"> </up-tag> -->

					<view class="w-[150rpx] flex justify-center bg-[#0052A8] rounded-[16rpx] py-[10rpx]"
						:style="{color: item.checked ? '#fff' :'#666', backgroundColor: item.checked ? '#0052A8' :'transparent'}"
						@click="radioClick(index)">
						{{ item.text }}
					</view>
				</up-grid-item>
			</up-grid>
		</view>

		<up-form ref="uFormRef" labelPosition="left" :rules="rules" :model="submitForm">
			<view class="flex items-center bg-[#ffffff] rounded-[16rpx] mt-[36rpx] px-[20rpx] py-[36rpx]">
				<view class="text-lg">血糖</view>
				<view class="flex-1 pl-[36rpx]">
					<up-form-item label="" prop="sugar" labelWidth="0rpx">
						<view style="width: 100%;" @click="showPicker=true">
							<up-input v-model="submitForm.sugar" placeholder="输入血糖值" border="none" readonly />

							<!-- -->
						</view>
					</up-form-item>
				</view>

				<view class="flex flex-row items-center">
					<view class="px-[12rpx] text-[#999]">mmol/L</view>
					<uni-icons type="right" size="32" color="#014777"></uni-icons>
				</view>
			</view>

			<view class="flex items-center bg-[#ffffff] rounded-[16rpx] mt-[36rpx] px-[20rpx] py-[36rpx]">
				<view class="text-lg">时间</view>
				<view class="flex-1 pl-[36rpx]">
					<up-form-item label="" prop="datetime" labelWidth="0rpx">
						<uni-datetime-picker type="datetime" v-model="submitForm.datetime" />
					</up-form-item>
				</view>

				<uni-icons type="right" size="32" color="#014777"></uni-icons>
			</view>

		</up-form>

		<view class="flex flex-1 justify-center text-white bg-[#0052A8] rounded-[16rpx] mt-[50rpx] px-[20rpx] py-[36rpx]"
			@click="doSubmit()">
			确定
		</view>

		<numeric-picker :show="showPicker" dataType="float1" :range="[5.2,88.2]" @onCancel="showPicker=false"
			@onConfirm="onConfirm"></numeric-picker>
	</view>
</template>

<script setup lang="ts">
	import moment from 'moment';
	import { onLoad, onShow, onReady } from "@dcloudio/uni-app";
	import { onMounted, ref, reactive, computed, watch } from 'vue';
	import type { UniFormRef } from '@/uni_modules/uview-plus/types'
	const uFormRef = ref<UniFormRef | null>(null)
	import type { IHealthObsResp } from '@/models/healthMeasure'
	import type { CommonTag } from '@/models/common'
	import { createBgRecord } from '@/common/api/measure';
	import {
		getMonthTagList,
		fillKeyNameByTypeData
	} from "@/model_static/history";

	import {
		getDateMeasureRecord,
		getMonthMeasureRecord
	} from '@/common/api/measure'

	const props = withDefaults(
		defineProps<{
			viewMode : string;
			dataType : string;
		}>(),
		{
			//没有默认值就是必填项
		}
	);

	interface MySubmitForm {
		sugar : string
		datetime : string
		action : string
	}
	const submitForm = ref<MySubmitForm>(
		{
			sugar: "",
			datetime: "",
			action: "空腹"
		})

	const actionList = ref<CommonTag[]>(
		[
			{
				text: '空腹',
				value: 1,
				checked: true,
				disable: false
			}, {
				text: '餐前',
				value: 2,
				checked: false,
				disable: false
			}, {
				text: '餐后',
				value: 3,
				checked: false,
				disable: false
			}
		]
	);
	const rules = {
		'sugar': {
			type: 'string',
			required: true,
			message: '请输入血糖值',
			trigger: ['blur'],
		},
		'datetime': {
			type: 'string',
			required: true,
			message: '请选择时间',
			trigger: ['blur', 'change'],
		}
	};

	const showPicker = ref(false);

	const radioClick = (myIndex : number) => {
		console.log('radioClick', myIndex);
		submitForm.value.action = actionList.value[myIndex].text;

		actionList.value.forEach((item, index) => {
			item.checked = index === myIndex;
		});
	};

	const onConfirm = (selValue : string) => {
		console.log("onConfirm", selValue);

		if (!selValue) {
			uni.showToast({
				title: "请选择有效的数值",
				icon: "none"
			})
			return
		}
		submitForm.value.sugar = selValue;
		showPicker.value = false;
	};

	const doSubmit = () => {
		console.log("submitForm: " + JSON.stringify(submitForm.value));
		if (!uFormRef.value) {
			return
		}
		uFormRef.value.validate().then((valid : boolean) => {
			console.log(valid);
			if (valid) {
				let checkAction: any = null;
				for (var i = 0; i < actionList.value.length; i++) {
					if (actionList.value[i].checked) {
						checkAction = actionList.value[i]
						break
					}
				}

				//请求接口 
				let bodyParams = {
					"measureTimeStr": moment(submitForm.value.datetime).format("YYYY-MM-DD HH:mm"),
					"personId": selectMenberType.value?.personId,
					"personName": selectMenberType.value?.personName,
					"bg": submitForm.value.sugar,
					"timeFrame": checkAction.value,
					"timeFrameName": checkAction.text
				}
				createBgRecord(bodyParams).then((res) => {
					uni.showToast({
						title: '提交成功',
						icon: 'none'
					})

					submitForm.value = {
						sugar: '',
						datetime: "",
						action: "无"
					};

				}).catch((error : any) => {

					console.log("error: " + JSON.stringify(error));
					// 处理验证错误  
					uni.showToast({
						title: "提交失败",
						icon: "none"
					})
				});
			}
		}).catch((error : any) => {

			console.log("error: " + JSON.stringify(error));
			// 处理验证错误  
			uni.showToast({
				title: "请规范完成表单录入",
				icon: "none"
			})
		})
	}

	watch(
		() => props.viewMode,
		(value) => {
			console.log('props.viewMode', props.viewMode);

		},
		{ deep: false, immediate: true }
	);

	const selectMenberType = ref<{ personName : string, personId : string | number }>();
	onMounted(() => {
		let lastUserInfo = uni.getStorageSync("lastSelectedUserInfo");
		let userInfo = uni.getStorageSync("userInfo");

		if (!lastUserInfo || !lastUserInfo.personId) {
			if (!userInfo || !userInfo.personId) {
				uni.showModal({
					content: "用户信息获取失败，请重新登录",
					success: (res) => {
						if (res.confirm) {
							uni.reLaunch({
								url: '/pages/login',
							})
						}
					}
				})
				return
			} else {
				selectMenberType.value = { personId: userInfo.personId, personName: userInfo.personInfo.personName }
			}
		} else {
			selectMenberType.value = { personId: lastUserInfo.personId, personName: lastUserInfo.personName }
		}

	});
</script>

<style lang="scss" scoped>
	.u-page__tag-item {
		margin-right: 20px;
	}

	.mask {
		width: 100%;
		height: 100vh;
		position: fixed;
		top: 0;
		left: 0;
		background: #000;
		z-index: 900;
		opacity: 0.3;
	}

	.other_popup {
		width: 100%;
		background-color: #fff;
		border-radius: 10upx 10upx 0 0;
		position: fixed;
		left: 0;
		bottom: -1000upx;
		z-index: 999;
		transition: all 0.3s;
	}
</style>