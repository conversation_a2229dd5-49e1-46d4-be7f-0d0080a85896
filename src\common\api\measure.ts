import request from '@/common/request'
import type { ILoginResp } from '@/models/healthMeasure'

/**
 * 查询家庭成员的体征测量记录 
 * @param 主类型 
 */
export const getLatsRecordByType = (body : any) => {
	return request({
		method: 'POST',
		url: '/monitor/need/needSign/queryPersonSignRecord',
		body: body,
		hideLoading: false,
		custom: {
			ignoreRespInterceptors: false, //忽略本次请求的响应拦截
			isMockApi: false //需要返回本地mock数据时请配置为true
		}
	})
}

/**
 * 按日期获取检测数据 
 * @param 日期 
 */
export const getDateMeasureRecord = (date : { date : string }) => {
	return request({
		method: 'GET',
		url: '/need/measure/dateRecord',
		body: date,
		hideLoading: false,
		custom: {
			ignoreRespInterceptors: true //忽略本次请求的响应拦截
		}
	})
}

/**
 * 按月份获取检测数据
 * @param data 月份
 */
export const getMonthMeasureRecord = (month : { month : string }) => {
	return request({
		method: 'GET',
		url: '/need/measure/monthRecord',
		body: month,
		hideLoading: false,
		custom: {
			ignoreRespInterceptors: true //忽略本次请求的响应拦截
		}
	})
}


/**
 * 新增血压记录
 * @param data  
 */
export const createBpRecord = (body : any) => {
	return request({
		method: 'POST',
		url: '/monitor/need/needSign/createBpRecord',
		body: body,
		hideLoading: false,
		custom: {
			//ignoreRespInterceptors: true //忽略本次请求的响应拦截
		}
	})
}
/**
 * 新增血糖记录
 * @param data  
 */
export const createBgRecord = (body : any) => {
	return request({
		method: 'POST',
		url: '/monitor/need/needSign/createBgRecord',
		body: body,
		hideLoading: false,
		custom: {
			//ignoreRespInterceptors: true //忽略本次请求的响应拦截
		}
	})
}

/**
 * 新增体脂记录
 * @param data  
 */
export const createBmiRecord = (body : any) => {
	return request({
		method: 'POST',
		url: '/monitor/need/needSign/createBmiRecord',
		body: body,
		hideLoading: false,
		custom: {
			//ignoreRespInterceptors: true //忽略本次请求的响应拦截
		}
	})
}


/**
 * 新增体温记录
 * @param data  
 */
export const createTempRecord = (body : any) => {
	return request({
		method: 'POST',
		url: '/monitor/need/needSign/createTempRecord',
		body: body,
		hideLoading: false,
		custom: {
			//ignoreRespInterceptors: true //忽略本次请求的响应拦截
		}
	})
}