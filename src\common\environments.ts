const env : string | undefined = process.env.NODE_ENV
const SERVE_CTX : any = {
	AUTH: '',
}
const configs : any = {
	// 生产环境
	production: {
		//url: 'https://need.service.i-need.com.cn:39002'
		url:'https://need.service.i-need.com.cn:39002'

	},
	// 开发环境
	development: {
		// #ifdef  H5
		//url: 'https://need.service.i-need.com.cn:39002',
		url:'https://need.service.i-need.com.cn:39002',
		// #endif

		//@ts-ignore
		// #ifdef  APP-PLUS | MP-WEIXIN
		//url: 'https://need.service.i-need.com.cn:39002'
		url:'https://need.service.i-need.com.cn:39002',
		// #endif
	},
}

const envConfigs : {
	url : string
} = configs[env as string]

export default { envConfigs, SERVE_CTX }