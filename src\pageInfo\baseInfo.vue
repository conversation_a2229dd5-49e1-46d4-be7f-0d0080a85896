<template>
    <view class="p-[30rpx]">
        <view class="bg-white rounded-[20rpx] p-[30rpx] mt-[30rpx]">
            <uni-forms ref="customForm" :modelValue="formData" :rules="customRules" :label-width="100" class="mt-[30rpx] px-[20rpx]">

                <uni-forms-item label="昵称" name="name">
                    <input v-model="formData.personName" disabled class="!w-auto h-35px !py-0px ![border:1px_solid_#e5e5e5] !font-size-14px !px-2" placeholder="请输入姓名" />
                </uni-forms-item>
                <uni-forms-item label="姓名" required name="name">
                    <input v-model="formData.realName" class="!w-auto h-35px !py-0px ![border:1px_solid_#e5e5e5] !font-size-14px !px-2" placeholder="请输入姓名" />
                </uni-forms-item>
                <uni-forms-item label="性别">
                    <uni-data-picker v-model="formData.sex" :localdata="typeData" popup-title="选择性别">
                    </uni-data-picker>
                </uni-forms-item>
                <uni-forms-item label="出生日期">
                    <uni-datetime-picker type="date" return-type="timestamp" v-model="formData.birthday" />
                </uni-forms-item>
                <uni-forms-item label="身高(cm)" name="height">
                    <input v-model="formData.height" class="!w-auto h-35px !py-0px ![border:1px_solid_#e5e5e5] !font-size-14px !px-2" placeholder="请输入身高" />
                </uni-forms-item>
                <uni-forms-item label="体重(kg)" name="weight">
                    <input v-model="formData.weight" class="!w-auto h-35px !py-0px ![border:1px_solid_#e5e5e5] !font-size-14px !px-2" placeholder="请输入体重" />
                </uni-forms-item>
                <uni-forms-item label="手机号码">
                    <input v-model="formData.phone" class="!w-auto h-35px !py-0px ![border:1px_solid_#e5e5e5] !font-size-14px !px-2" placeholder="请输入手机号" />
                </uni-forms-item>
                <!-- <uni-forms-item label="与本人的关系">
					<uni-data-picker v-model="formData.relation" :localdata="relationData" popup-title="请选择与你的关系">
					</uni-data-picker>
				</uni-forms-item> -->
            </uni-forms>

        </view>

        <view>
            <button @click="submit()" size="default" class="text-[#fff] bg-[#2B97E5] rounded-[50rpx] mt-[50rpx]">
                完成
            </button>
        </view>

    </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { editPerson } from "@/common/api/family";
import { useUserInfo } from "@/stores/userInfo";

const currentPersonInfo = uni.getStorageSync("userInfo")?.personInfo || {};
const personId = uni.getStorageSync("userInfo")?.personId || {};
console.log(currentPersonInfo);

const typeData = [
    {
        text: "女",
        value: "10001",
    },
    {
        text: "男",
        value: "10002",
    },
    {
        text: "未知",
        value: "10003",
    },
];
const relationData = [
    {
        text: "本人",
        value: "10001",
    },
    {
        text: "配偶",
        value: "10002",
    },
    {
        text: "父母",
        value: "10003",
    },
    {
        text: "子女",
        value: "10004",
    },
];
// 校验表单数据
const formData = ref({
    personName: currentPersonInfo?.personName,
    realName: currentPersonInfo?.realName,
    sex: currentPersonInfo?.sex,
    birthday: currentPersonInfo?.birthday,
    phone: currentPersonInfo?.phone,
    height: currentPersonInfo?.height,
    weight: currentPersonInfo?.weight,
});
// 自定义表单校验规则
const customRules = {
    realName: {
        rules: [
            {
                required: true,
                errorMessage: "姓名不能为空",
            },
        ],
    },
    // phone: {
    //     rules: [
    //         {
    //             required: true,
    //             errorMessage: "手机号不能为空",
    //         },
    //         {
    //             pattern: /^1[3-9]\d{9}$/, // 匹配中国大陆的手机号码格式
    //             errorMessage: "域名格式错误",
    //         },
    //     ],
    // },
};
// 创建一个 ref 对象，用于绑定组件的引用
const customForm = ref();

const submit = () => {
    console.log("------");
    if (customForm.value) {
        customForm.value
            .validate()
            .then((res: any) => {
                console.log(formData.value);
                let params = {
                    createUserId: personId, 
                    ...formData.value}
                editPerson(params)
                    .then((res) => {
                        uni.showToast({
                            title: "保存成功",
                            icon: "success",
                        });
                    })
                    .catch(() => {
                        uni.showToast({
                            title: "保存失败",
                            icon: "none",
                        });
                    });
            })
            .catch((err: any) => {
                console.log("err", err);
            });
    } else {
        //出错了
    }
};
/*  const handleSubmit = () => {
  console.log("------");
  if (valiForm.value) {
    valiForm.value.validate().then((res) => {
      console.log('表单数据信息：', res);
    }).catch((err) => {
      console.log('表单错误信息：', err);
    });
  }
}; */
/*onMounted(() => {
  if (formRef.value) {
    formRef.value.setRules(formRef);
  }
}); */
</script>

<style scoped lang="less">
</style>