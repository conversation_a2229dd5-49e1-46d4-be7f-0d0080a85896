<template>
	<view class="page">
		<view class="m-[30rpx]">
			<view class="w-full rounded-[25rpx] bg-[#ffffff]">
				<radio-group @change="radioChange">
					<label class="flex flex-row justify-between items-center h-[120rpx] px-[20rpx]"
						v-for="(item, index) in radioOptions" :key="item.value">
						<view>{{item.name}}</view>
						<view>
							<radio :value="item.value" :checked="item.value === currentRadio" />
						</view>
					</label>
				</radio-group>
			</view>


			<view class="font-bold mt-[28rpx] mb-[12rpx]">{{ currentRadio === 1 ? '选择日期' : '选择间隔' }}</view>

			<view class="flex flex-col rounded-[25rpx] bg-[#ffffff]">
				<!-- 固定间隔弹出框 -->
				<view v-if="currentRadio === 1" class="flex flex-col items-center">
					<picker-view class="picker-view" :indicator-style="'height: 50px'" @change="pickerViewChange">
						<picker-view-column>
							<view class="item" v-for="(item,index) in intervalOptions" :key="index">{{item.text}}</view>
						</picker-view-column>
					</picker-view>
				</view>

				<view v-else class="flex flex-col p-[20rpx]">
					<!-- <view class="week-options">
						<view class="rounded-lg bg-[#EFEFEF] p-2 text-center m-2" v-for="(item, index) in weekDays" :key="index">
							{{ item }}
						</view>
					</view> -->
					<up-grid :border="false" col="4" gap="12rpx" align="center">
						<up-grid-item v-for="(item,index) in weekDays" :key="index">
							<up-tag class="my-[12rpx]" style="min-width: 48rpx;" :text="item.text" shape="circle"
								:bgColor="item.checked ? '#0081ff' : '#ccc'" :borderColor="item.checked ? '#0081ff' : '#ccc'"
								@click="weekRadioClick(index)"> </up-tag>
						</up-grid-item>
					</up-grid>
				</view>

			</view>

			<view>
				<button @click="confirm()" size="default" class="text-[#fff] bg-[#2B97E5] rounded-[50rpx] mt-[50rpx]">
					完成
				</button>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { ref } from 'vue';
	import type { CommonTag } from '@/models/common'

	interface radioOptionsItem {
		value : number
		name : string
		checked : boolean
	};

	const radioOptions = ref<radioOptionsItem[]>([{
		value: 1,
		name: '固定间隔',
		checked: true
	},
	{
		value: 2,
		name: '每周特定日期',
		checked: false
	}]);
	const currentRadio = ref<number>(1);
	// 控制选择项
	const selectedOption = ref<string>(''); // 默认没有选中

	// 固定间隔选项 ['每天', '每隔一天', '每3天']
	const intervalOptions = [
        {
            text: '每天',
            value: 1,
        }, {
            text: '每隔一天',
            value: 2,
        }, {
            text: '每3天',
            value: 3,
        }
    ];
	const intervalValue = ref(intervalOptions[0])
	let intervalResult = {};

	// 每周特定日期选项
	const weekDays = ref<CommonTag[]>([{
		text: '周一',
		value: 0,
		checked: false,
		disable: false
	}, {
		text: '周二',
		value: 1,
		checked: false,
		disable: false
	}, {
		text: '周三',
		value: 2,
		checked: false,
		disable: false
	}, {
		text: '周四',
		value: 3,
		checked: false,
		disable: false
	}, {
		text: '周五',
		value: 4,
		checked: false,
		disable: false
	}, {
		text: '周六',
		value: 5,
		checked: false,
		disable: false
	}, {
		text: '周日',
		value: 6,
		checked: false,
		disable: false
	}]);
	let weekDayResult : any[] = [];

	const radioChange = (evt : any) => {
        console.log(evt);
        let value = Number(evt.detail.value)
        if(typeof(value) === 'number') {
            currentRadio.value = value
        }
		// for (let i = 0; i < radioOptions.value.length; i++) {
		// 	if (radioOptions.value[i].value === evt.detail.value) {
		// 		currentRadio.value = i;
		// 		break;
		// 	}
		// }

		//console.log(currentRadio.value);
	}

	const pickerViewChange = (item : any) => {

		console.log("pickerViewChange", item);
		intervalResult = item
		console.log(intervalResult);
	}

	const weekRadioClick = (myIndex : number) => {
		console.log('radioClick', myIndex);
		weekDayResult = [];
		weekDays.value.forEach((item, index) => {
			if (index === myIndex) {
				item.checked = !item.checked
			} /* else{
				item.checked = true
			} */


			if (item.checked) {
				weekDayResult.push(item.value);
			}
		});

		console.log(weekDays.value);
	};

	const confirm = () => {
        let targetObj = {
            intervalType: currentRadio.value
        }
        if (currentRadio.value === 1) {
            console.log("confirm000", intervalResult , intervalValue.value);
            targetObj.dayInterval = intervalResult?.value || intervalValue.value?.value;
            targetObj.dayIntervalText = intervalResult?.text || intervalValue.value?.text;
            uni.$emit('drugFrequecyEvent', targetObj);
        } else if (currentRadio.value === 2) {
            console.log("confirm111", weekDayResult.join(','));
            targetObj.weekDays =  weekDayResult.join(',');
            uni.$emit('drugFrequecyEvent', targetObj);
        } else {
			//...
		}
		// if (currentRadio.value === 1) {
		// 	console.log("confirm000", intervalResult , intervalValue);
		// 	uni.$emit('drugFrequecyEvent', intervalResult || intervalValue);
		// } else if (currentRadio.value === 2) {
		// 	console.log("confirm111", weekDayResult.join(','));
		// 	uni.$emit('drugFrequecyEvent', weekDayResult.join(','));
		// } else {
		// 	//...
		// }

		uni.navigateBack({})
	}
</script>

<style scoped lang="scss">
	.list-border:after {
		position: absolute;
		bottom: 0;
		right: 0;
		left: 0;
		height: 1px;
		content: "";
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5);
		background-color: #bbb;
	}

	.picker-view {
		width: 650rpx;
		height: 500rpx;
		margin-top: 20rpx;
	}

	.item {
		line-height: 100rpx;
		text-align: center;
	}
</style>