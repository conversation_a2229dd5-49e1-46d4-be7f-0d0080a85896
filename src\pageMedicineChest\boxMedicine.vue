<template>
	<view>
		<uni-card>
			<view class="flex flex-row items-center py-[12rpx]" v-for="(medicine, index) in medicineBox" :key="index">
				<view>
					<image :src="`/static/medecine-type/${getImageName(medicine.type)}-type.png`"
						class="w-[120rpx] h-[120rpx] object-contain bg-[#01558D] rounded-lg" />
				</view>
				<view class="relative flex flex-row flex-1 justify-between list-border items-center  h-[120rpx]">
					<view class="flex flex-col">
						<text class="flex-1 pl-[20rpx] text-lg text-[#333]">{{ medicine.name }}</text>
						<view class="flex flex-row">
							<text class="flex-1 pl-[20rpx]">{{ medicine.type }}</text>
							<uni-link class="pl-[20rpx]" href="#" text="说明书" color="#01558D"></uni-link>
						</view>
					</view>
					<!-- <uni-icons type="right"></uni-icons> -->
					<!-- 复选框组 -->
					<!--选中图标-->
					<checkbox-group @change="checkSelectItem($event, medicine)">
						<label>
							<checkbox :value="medicine.id" :checked="medicine.checked" />
						</label>
					</checkbox-group>
				</view>
			</view>
		</uni-card>
		<!--按扭-->
		<view class="px-5">
			<view class="grid grid-cols-2 gap-2">
				<up-button type="primary" icon="trash" text="删除" :disabled="deleteDisable" plain @click="doDelete"></up-button>
				<up-button type="primary" :icon="isCheckAll ? 'checkmark-circle-fill' : 'checkmark-circle'" text="全选" plain
					@click="toggleCheckAll"></up-button>
				<!-- <view class="custom-style-border py-2">
					<checkbox-group @change="checkSelect">
						<label style="display: flex;">
							<checkbox :checked="checkTrue"></checkbox>
							<text>全选</text>
						</label>
					</checkbox-group>
				</view> -->
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { ref } from "vue";
	import type { MedicineBoxItem } from '@/models/medicineChest'
	const checkTrue = ref(false);  // 全选状态的响应式变量
	const getImageName = (type : any) => {
		switch (type) {
			case '药片':
				return 'pian';
			case '胶囊':
				return 'jiaonang';
			case '液体':
				return 'yiti';
			case '软膏':
				return 'ruangao';
			case '设备':
				return 'shebei';
			default:
				return 'default'; // 如果没有匹配到类型，使用默认图片
		}
	}
	const medicineBox : MedicineBoxItem[] = [
		{

			id: '1',
			name: "阿莫西林",
			type: "胶囊",
			description: "用于治疗细菌感染的抗生素",
			status: "已结束",  // 后端返回状态
		},
		{

			id: '2',
			name: "布洛芬",
			type: "液体",
			description: "用于缓解感冒症状",
			status: "计划中",  // 后端返回状态
		}
	];
	medicineBox.forEach((item : MedicineBoxItem) => {
		Object.assign(item, { checked: false })
	})

	const deleteDisable = ref(true);
	const isCheckAll = ref(false);
	const toggleCheckAll = () => {
		isCheckAll.value = !isCheckAll.value;
		deleteDisable.value = !isCheckAll.value;

		medicineBox.forEach((item : MedicineBoxItem) => {
			if (isCheckAll.value) {
				item.checked = true
			} else {
				item.checked = false
			}
		})
	};
	const checkSelectItem = (e : any, item : MedicineBoxItem) => {
		// 如果是取消选中，就把checked置为false,如果是选中，就给添加一个checked=true
		item.checked = !item.checked;

		// 过滤出数组中checked为true的项目，如果和原RecordList长度相等，就全选显示
		let newArr = medicineBox.filter(item => (item.checked == true))

		if (newArr.length === 0) {
			deleteDisable.value = true;
		} else {
			deleteDisable.value = false;
		}

		if (newArr.length === medicineBox.length) {
			isCheckAll.value = true;
		} else {
			isCheckAll.value = false;
		}
	};

	const doDelete = () => {
		let checkedIds = medicineBox.filter(item => item.checked).map(item => item.id);
		console.log(checkedIds);

		if (checkedIds.length == 0) {
			uni.showModal({
				content: "请选择删除的药品。"
			})
			return
		}

		uni.showModal({
			content: "确定删除所选药品吗？",
			success: (res) => {
				if (res.confirm) {
					let idsString = checkedIds.join(',');
					console.log(idsString);
				}
			}
		})
	}
</script>

<style scoped lang="scss">
	.list-border:after {
		position: absolute;
		bottom: 0;
		right: 0;
		left: 0;
		height: 1px;
		content: "";
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5);
		background-color: #bbb;
	}

	.custom-style-border {
		width: 180rpx;
		border: 1px solid #3c9cff;
	}
</style>