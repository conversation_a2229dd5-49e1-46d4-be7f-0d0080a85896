<template>
	<view class="container">
		<view class="img-box">
			<image class="img" mode="widthFix" src="/static/img/logo-white.png"></image>
		</view>
		<view class="plan">
			<uni-easyinput type="text" v-model="form.phoneNumber" placeholder="请输入您的手机号" placeholderStyle="color: #718096;"
				class="input" :inputBorder="false"></uni-easyinput>

			<view class="agreed">
				<checkbox value="true" :checked="isCheck" /> <text @click="onClickCheck">我已阅读并同意</text><text class="text-blue"
					@click="onClickPrivacyProtocol(1)">用户协议</text>和<text class="text-blue"
					@click="onClickPrivacyProtocol(2)">隐私政策</text>
			</view>
		</view>
		<view class="xx-button" @click="onClickLogin" :style="{ background: isCheck ? '#0052A8' : '#b6bcbf' }">登录</view>


		<view class="text-register" @click="isAutoLogin" style="margin-right: 30rpx;">一键登录 →</view>

	</view>
</template>
<script lang="ts" setup>
	import type { ILogin, ILoginResp } from '@/models/login'
	import { useUserInfo } from '@/stores/userInfo'
	import { ref, reactive } from 'vue'
	import { signIn } from '@/common/api/login'
    import { onLoad } from "@dcloudio/uni-app";


	const isCheck = ref<boolean>(true);
	// 自定义表单数据
	const form = ref<ILogin>({
		phoneNumber: '13807879514',
	});
	/* 我已阅读并同意协议 */
	const onClickCheck = () => {
		isCheck.value = isCheck.value
	}
	const onClickPrivacyProtocol = (type : number) => {
		if (type === 1 || type === 2) {
			uni.navigateTo({
				url: 'privacyAndProtocol?type=' + type,
				animationType: 'slide-in-right'
			})
		} else {
			uni.showToast({
				title: '未知的意图 ',
				icon: 'none'
			})
		}
	}
	const onClickLogin = () => {
		if (!isCheck.value) {
			uni.showToast({
				title: '请阅读并勾选协议',
				icon: 'none'
			})
		}
		if (!form.value.phoneNumber) {
			uni.showToast({
				title: '请输入手机号',
				icon: 'none'
			})
			return
		}
		bind(form.value);
	}
	/* 一键登录TODO */
	const isAutoLogin = () => {
        uni.login({
            provider: 'univerify',
            univerifyStyle: {
                fullScreen: true
            },
            success(res) {
				console.log(res)
                const params = {
                    access_token: res.authResult.access_token, // 客户端一键登录接口返回的access_token
                    openid: res.authResult.openid // 客户端一键登录接口返回的openid
                }
                bind(params)
            },
            fail(res) {  // 登录失败
				console.log(res)
                let errText = res.errMsg
                if (res.code == -20202) {
                    errText = '未开启蜂窝网络'
                }else if(res.code == -20201){ 
                    errText = '未插手机电话卡'
                }
                uni.showToast({
                    title: errText,
                    icon: "none"
                })
                setTimeout(() => {
                    uni.closeAuthView() //关闭一键登录弹出窗口
                }, 500)
            }
        })
	}
	/* 去注册 */
	const onClickRegister = () => {
		uni.navigateTo({
			url: '/',
			animationType: 'slide-in-bottom'
		})
	}

	const onClickProtoSure = () => {

	}

	let clearStatus1 = ref(false)
	let clearStatus2 = ref(false)
	const showClear = (status, state) => {
		state = state ? true : false

		setTimeout(function () {
			if (status === 1) {
				clearStatus1.value = state
			} else {
				clearStatus2.value = state
			}

			//验证
		}, 100)
	}
	/* 登录 */
	const bind = (data: any) => {
        uni.showLoading({
            title: '登录中...',
            mask: true
        })
        uniCloud.callFunction({
            name: 'univerify', // 函数名称
            data
        }).then(res => {
            const { success, data, msg } = res.result

            if (!success) {
                uni.showToast({
                    title: msg,
                    icon: 'none'
                })
                return
            }
            // 登录成功
            useUserInfo().setUserInfos(data as ILoginResp)
            useUserInfo().setToken(data?.token || '')
            goHome()
        }).catch(err => {
            console.log(err)
        }).finally(() => {
            // #ifdef APP-PLUS
            uni.closeAuthView() //关闭一键登录弹出窗口
            // #endif
            uni.hideLoading();
        })
	}
	const goHome = () => {
		//要是使用reLaunch，否则 tabbar不生效
		uni.reLaunch({
			url: '/pages/home/<USER>',
		})
	}
    onLoad(() => {
        // isAutoLogin()
    })
</script>

<style lang="scss" scoped>
	.container {
		background: white;
		height: 100%;
		text-align: center;
		padding: 120rpx 40rpx 0;

		.img {
			width: 360rpx;
		}

		input::-webkit-input-placeholder {
			/* WebKit browsers */
			color: red;
		}

		.plan {
			padding: 40rpx 40rpx 60rpx;
			border-radius: 12px;
			background: rgba(255, 255, 255, 1);
			box-shadow: 0px 4px 20px rgba(5, 130, 202, 0.1);

			.input {
				border-bottom: 1px solid #DCEDFF;
				font-size: 32rpx;
				padding-bottom: 18rpx;
				margin-bottom: 34rpx;
			}
		}

		.agreed {
			font-size: 28rpx;
			color: #A3BCD8;
			line-height: 100rpx;
			text-align: left;

			::v-deep .uni-checkbox-input {
				border-radius: 50%;
				width: 36rpx;
				height: 36rpx;
				margin-right: 14rpx;
			}
		}

		.img-box {
			height: 300rpx;
			margin-bottom: 60rpx;
		}

		.text-blue {
			color: #0D5BAB;
		}

		>.xx-button {
			border-radius: 28rpx;
			height: 108rpx;
			line-height: 108rpx;
			font-size: 40rpx;
			font-weight: bold;
			margin: 0 20rpx;
			position: relative;
			top: -40rpx;
			background: #0052A8;
			border: none;
			color: white;
		}

		.text-register {
			color: #0D5BAB;
			display: inline-block;
		}

		::v-deep .uni-popup__wrapper {
			margin: 36rpx;
			overflow: auto;
			border-radius: 20rpx !important;
		}

		.popup-content {
			text-align: left;
			overflow: auto;
			padding: 20rpx;

			.text {
				text-indent: 2rem;
			}
		}
	}
</style>