<template>
    <u-popup 
    :show="popupVisible" 
    @close="close" 
    @open="open" 
    :closeOnClickOverlay="closeOnClickOverlay"
    :round="round"
    :overlayOpacity="overlayOpacity"
    :safeAreaInsetBottom="true">
        <view class="popup-container">
            <view class="popup-title">
                <text>{{ title }}</text>
                <text class="iconfont icon-guanbi1" @click="popupVisible = false"></text>
            </view>
            <view class="popup-content">
                <slot></slot>
            </view>
            <view class="popup-footer">
                <slot name="footer"></slot>
            </view>
            <view class="loading-mark" v-if="loading">
                <text class="iconfont icon-loading icon-rotating"></text>
            </view>
        </view>
    </u-popup>
</template>
<script>
export default {
    name: "Popup",
    props: {
        visible: Boolean,
        loading: false,
        title: {
            default: '标题'
        },
        closeOnClickOverlay: true,
        round: {
            default: 16
        },
        overlayOpacity: {
            default: 0.5
        }
    },
    computed: {
        popupVisible: {
            get: function() {
                return this.visible;
            },
            set: function(val) {
                this.$emit('update:visible', val);
            }
        }
    },
    methods: {
        close() {
            this.popupVisible = false;
            this.$emit('close')
        },
        open() {
            this.$emit('open')
        }
    },
    mounted() {
    }
}
</script>
<style lang="scss" scoped>
.popup-container{
    height: 100%;
	.popup-title {
        position: relative;
        color: #1A202C;
		font-size: 32rpx;
		text-align: center;
		margin-top: 40rpx;
        font-weight: bold;
		.iconfont{
            display: inline-block;
            padding: 14rpx;
			color: #333333;
			position: absolute;
			right: 32rpx;
            top: -6rpx;
			font-size: 32rpx;
			font-weight: bold;
		}
	}
	.popup-content{
		padding: 0 60rpx 0;
		margin-top: 40rpx;
        min-height: 340rpx;
	}
	.popup-footer{
		display: flex;
		justify-content: space-evenly;
		padding: 40rpx 0;
		border-top: 2rpx solid #eee;
		.button{
			width: 120rpx;
			min-width: 120rpx;
			border-radius: 30px;
			&.close{
				background: #F3F3F5;
				color: #147BD1;
			}
		}
	}
    .loading-mark {
        position: absolute;
        top: 80rpx;
        left: calc(50% - 50px);
        width: 100px;
        text-align: center;
        font-size: 32px;
        
        .iconfont {
            color: #0052A8;
            font-weight: bold;
            font-size: 48rpx;
        }
    }
}
</style>