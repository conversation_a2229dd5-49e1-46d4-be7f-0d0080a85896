<template>
    <view class="container w-full h-full">
        <view class="box">
            <view class="item" v-for="(item, index) in list" :key="index" @click="onClickItem(item)">
                <view class="left">{{ item.left }}</view>
                <view class="right">
                    <text class="right-text">{{ item.rightText }}</text>
                    <!-- <text class="right-icon" :class="item.rightIcon"></text> -->
                    <uni-icons type="right" size="20" color="#999"></uni-icons>
                </view>
            </view>
        </view>
        <view class="submit-btn" @click="onClickOut">
            <text class="text">退出登录</text>
        </view>

    </view>
</template>
<script>
import { useUserInfo } from '@/stores/userInfo'
import { downloadNewVersion } from '@/common/checkappupdate'
export default {
    data() {
        return {
            list: [
                { left: '权限', rightText: '', rightIcon: 'iconfont icon-xuanzeqixiayige_o' },
                { left: '通知', rightText: '', rightIcon: 'iconfont icon-xuanzeqixiayige_o' },
                { left: '意见反馈', rightText: '', rightIcon: 'iconfont icon-xuanzeqixiayige_o' },
                { left: '清理缓存', rightText: '', rightIcon: 'iconfont icon-xuanzeqixiayige_o', fun: 'clearCache' },
                { left: '关于我们', rightText: '', rightIcon: 'iconfont icon-xuanzeqixiayige_o', to: '/pageUseInfo/about' },
                { left: '版本更新', rightText: '', rightIcon: 'iconfont icon-xuanzeqixiayige_o', fun: 'checkVersion' },
            ],
        }
    },
    methods: {
        onClickItem(item) {
            if (item.fun) {
                this[item.fun]()
            }else if (item.to) {
                uni.navigateTo({
                    url: item.to
                })
            }
        },
        getVersion() {
            plus.runtime.getProperty(plus.runtime.appid, (widgetInfo) => {
                this.list[5].rightText = '当前版本：' + widgetInfo.version
            })
        },
        checkVersion() {
            // #ifdef APP-PLUS
            uni.showLoading({
                title: '加载中...',
                mask: true
            })
            plus.runtime.getProperty(plus.runtime.appid, (widgetInfo) => {
                const db = uniCloud.database();
                db.collection('version-update').limit(1).get().then(res => {
                    const data = res.result.data[0]

                    let versionCode = parseInt(widgetInfo.versionCode)
                    if (versionCode >= data.versionCode) {
                        uni.showToast({title: '已经是最新的版本！', icon: 'none'})
                        return
                    }
                    uni.showModal({
                        title: '升级版本' + data.version,
                        content: data.info,
                        showCancel: true,
                        confirmText: '更新',
                        cancelText: '不了',
                        success: (res) => {
                            if (!res.confirm) {
                                return
                            }
                            downloadNewVersion(data.url)
                        }
                    })
                }).finally(() => {
                    uni.hideLoading();
                })
            })
            // #endif
        },
        // 清理缓存
        clearCache() {
            // 清理缓存
            uni.showModal({
                title: '清理缓存',
                content: '是否要清理全部缓存？',
                showCancel: true,
                confirmText: '是的',
                cancelText: '不了',
                success: (res) => {
                    if (!res.confirm) {
                        return
                    }
                    // 缓存的 key
                    // uni.getStorageInfoSync()
                    // uni.removeStorageSync('not_update_app');
                    // uni.clearStorageSync();
                    uni.showToast({title: '清理成功'})
                }
            })
        },
        // 退出登录
        onClickOut() {
            
            uni.showModal({
                title: '提示',
                content: '确定要退出登录吗？',
                success: (res) => {
                    if (res.confirm) {
                        useUserInfo().removeUserInfos()
                        useUserInfo().removeToken()
                        
                        uni.redirectTo({
                            url: '/pages/login',
                        })
                    }
                }
            })
        },
    },
    mounted() {
        // #ifdef APP-PLUS
        this.getVersion()
        // #endif
    },
}
</script>
<style scoped lang="scss">
.container {
    .box {
        display: flex;
        flex-direction: column;
        background: white;
        margin: 30rpx 10rpx;
    }
    .item {
        font-size: 30rpx;
        display: flex;
        justify-content: space-between;
        height: 100rpx;
        line-height: 100rpx;
        margin-left: 32rpx;
        padding-right: 32rpx;
        border-bottom: 1px solid #eee;
    }

    .submit-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 140 * 2rpx;
        height: 36 * 2rpx;
        margin: 100rpx auto 50rpx;
        opacity: 1;
        border-radius: 4 * 2rpx;
        background: #1A71D0;

        .text {
            height: 27 * 2rpx;
            opacity: 1;
            /** 文本1 */
            font-size: 14 * 2rpx;
            font-weight: 400;
            letter-spacing: 0px;
            line-height: 26.06 * 2rpx;
            color: rgba(255, 255, 255, 1);
            text-align: center;
            vertical-align: top;
        }
    }
}
</style>