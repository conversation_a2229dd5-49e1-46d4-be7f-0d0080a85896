<template>
    <view class="p-[30rpx]">
        <view v-for="item in dataList" class="device-item flex relative bg-white p-[20rpx] rounded-[8px]">
            <image class="img w-[180rpx] h-[180rpx]" :src="imageDeviceList[item.deviceType]" mode="aspectFit"></image>
            <view class="flex-1 flex items-center pl-[20rpx]">{{ item.groupName }}  <text class="pl-[20rpx]">{{ item.deviceNo?.slice(-4) }}</text></view>


            <view class="absolute right-[20rpx] flex">
                <text class="dot" v-if="item.onlineState != 0"></text>
                <ElectricQuantity class="iconfont" :ElectricQuantity="item.capacityPercent" :isElectric="true"></ElectricQuantity>
                <view class="hover-more">
                    <up-icon name="more-dot-fill" color="#989898" size="32rpx" style="margin-right: 10rpx;">
                    </up-icon>
                    <view class="hover-box" v-if="showMoreBtn">
                        <view class="box-item" @click="changeDevice(item)">解绑设备</view>
                    </view>
                </view>
            </view>
        </view>

        <view v-if="!dataList.length">
            <image class="flex w-[70%]" style="margin: 0 auto;" src="@/static/img/empty1.png" mode="aspectFit"></image>
            <text class="flex justify-center color-[#666]">暂无设备</text>
        </view>
    </view>
</template>
<script setup lang="ts">
    import ElectricQuantity from '@/components/animation/ElectricQuantity.vue'; // 电池
    import { queryUserDevice, unbindDevice } from '@/common/api/device';
	import { ref } from "vue";
	import { onShow } from "@dcloudio/uni-app";

    import { useUserInfo } from '@/stores/userInfo'
	const storeUserInfo = useUserInfo();

    const dataList = ref<any[]>([]);

    const queryList = () => {
        queryUserDevice({userId: storeUserInfo.userId}).then((res : any) => {
            let data = res.data || []
            console.log(data)
            dataList.value = data
        })
    }


    const showMoreBtn = ref(true)
    const changeDevice = (data : any) => {
        showMoreBtn.value = false
        setTimeout(() => {
            showMoreBtn.value = true
        }, 300);
        // 提示是否解绑
        uni.showModal({
            title: '提示',
            content: '是否解绑设备?',
            success: (res) => {
                if (res.confirm) {
                    unbindDevice({ deviceNo: data.deviceNo, userId: storeUserInfo.userId }).then((res: any) => {
                        // 提示绑定成功
                        uni.showToast({
                            title: '解绑成功',
                            icon: 'success'
                        })
                        queryList()
                    })
                }
            }
        })
    }

    // 设备图片
    import device1 from '/static/products/thermometer02.png';
    import device2 from '/static/products/blood-pressure02.png';
    import device3 from '/static/products/blood-pressure01.png';
    import device4 from '/static/products/bool-oxygen01.png';

    const imageDeviceList: any = {
        BCB : "充电盒",
        DRIP : "点滴监护仪",
        HYO2 : device3,
        SPHY : device2,
        SPO2 : device4,
        THERMOMETER : device1,
        THERMOMETERSPLINCHED : device1
    }


	onShow(() => {
        queryList()
	});
</script>
<style lang="scss" scoped>
.device-item {
    ::v-deep(.battery) {
        &.battery {
            background: #989898;
        }
        &:after {
            background-color: #989898;
        }
    }
    .hover-more {
        position: relative;
        margin-left: 40rpx;
        .hover-box {
            display: none;
            position: absolute;
            right: 0;
            font-size: 28rpx;
            background: rgba(255, 255, 255, 1);
            box-shadow: rgba(0, 0, 0, 0.15) 0px 5px 15px 0px;
            border-radius: 8rpx;
            text-align: center;
            color: #0e9cd8;
            width: 200rpx;
            z-index: 2;
            animation: fadeIn 0.3s;
            .box-item {
                padding: 16rpx;
                border-bottom: 1px solid #d8d8d8;
                &:last-child {
                    border-bottom: none;
                }
            }
        }
        &:hover {
            .hover-box {
                display: block;
                
            }
        }
    }
}
</style>