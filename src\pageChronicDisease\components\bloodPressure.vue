<template>
	<view class="flex flex-col items-stretch m-[28rpx] px-[20rpx] py-[28rpx]">

		<view class="bg-white rounded-[16rpx] m-[15rpx] p-[30rpx]">
			<view class="flex items-center justify-between">
				<text class="text-base text-[#333]">早上</text>
			</view>

			<view class="charts-box">
				<qiun-data-charts type="area" :opts="chartsOpts" :chartData="morningChartData" />
			</view>
		</view>

		<view class="bg-white rounded-[16rpx] m-[15rpx] p-[30rpx]">
			<view class="flex items-center justify-between">
				<text class="text-base text-[#333]">晚上</text>
			</view>

			<view class="charts-box">
				<qiun-data-charts type="area" :opts="chartsOpts" :chartData="nightChartData" />
			</view>
		</view>

		<view class="grid grid-cols-2">
			<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[24rpx] py-[24rpx]" @click="gotoManualEntry(0)">
				<text class="font-bold">手动输入</text>
			</view>

			<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[24rpx] py-[24rpx]" @click="gotoMeasure">
				<text class="font-bold">测量血压</text>
			</view>
		</view>

		<view class="pt-[36rpx] pb-[12rpx] font-bold">血压异常记录</view>
		<!-- <view class="flex flex-col bg-[#ffffff] rounded-[16rpx] mt-[8rpx] px-[20rpx] py-[36rpx]">
			<up-grid :border="false" col="3" gap="12rpx" align="center">
				<up-grid-item v-for="(item,index) in actionList" :key="index">
					<view class="flex flex-col flex-1 pl-[20rpx]">
						<text class="text-lg">轻度</text>
						<view class="text-sm pt-[8rpx]">
							0次
						</view>
					</view>
				</up-grid-item>
			</up-grid> 
		</view> -->
		<view class="grid grid-cols-3">
			<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[12rpx] py-[20rpx]">
				<text class="font-bold text-[#641013]">重度</text>
				<text class="pt-[12rpx]">{{ respInfo.record.level1 }}次</text>
			</view>

			<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[12rpx] py-[20rpx]">
				<text class="font-bold text-[#951d1d]">中度</text>
				<text class="pt-[12rpx]">{{ respInfo.record.level2 }}次</text>
			</view>

			<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[12rpx] py-[20rpx]">
				<text class="font-bold text-[#bd3124]">轻度</text>
				<text class="pt-[12rpx]">{{ respInfo.record.level3 }}次</text>
			</view>

			<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[12rpx] py-[20rpx]">
				<text class="font-bold text-[#81b337]">正常</text>
				<text class="pt-[12rpx]">{{ respInfo.record.stateNormal }}次</text>
			</view>

			<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[12rpx] py-[20rpx]">
				<text class="font-bold text-[#347caf]">偏低</text>
				<text class="pt-[12rpx]">{{ respInfo.record.stateLow }}次</text>
			</view>

			<view class="flex flex-col items-center bg-white rounded-[16rpx] m-[12rpx] py-[20rpx]">
				<text class="font-bold text-[#333]">全部</text>
				<text class="pt-[12rpx]">{{ respInfo.record.stateAll }}次</text>
			</view>


		</view>

		<view class="pt-[36rpx] pb-[12rpx] font-bold">血压管理</view>
		<view class="flex flex-row items-center bg-white rounded-[16rpx] mt-[8rpx] py-[30rpx] px-[40rpx]" @click="gotoPlan">
			<!-- <image src="@/static/products/blood-pressure02.png" class="w-[80rpx] h-[80rpx] object-contain" /> -->
			<uni-icons customPrefix="iconfont" type="icon-xieya1" size="42" color="#f00a4f"></uni-icons>

			<view class="flex flex-col flex-1 pl-[20rpx]">
				<text class="text-lg">测量计划</text>
				<view class="text-sm pt-[8rpx]">
					按预设间隔时间，连续测量血压
				</view>
			</view>

			<view class="flex flex-row items-center">
				<uni-icons type="right" size="32" color="#014777"></uni-icons>
			</view>
		</view>


	</view>
</template>

<script setup lang="ts">
	import { onLoad, onShow, onReady } from "@dcloudio/uni-app";
	import { ref, reactive, computed, watch, onMounted } from 'vue';
	import { getBloodPressureOptions, getPulseValueOptions } from '@/model_static/bloodPresure'
	import type { IHealthObsResp } from '@/models/healthMeasure'
	import type { CommonTag } from '@/models/common'
	import {
		getMonthTagList,
		fillKeyNameByTypeData
	} from "@/model_static/history";

	import {
		getDateMeasureRecord,
		getMonthMeasureRecord
	} from '@/common/api/measure'
	import { nextTick } from 'process';

	/* const props = withDefaults(
		defineProps<{
			viewMode : string;
			dataType : string;
		}>(),
		{
			//没有默认值就是必填项
		}
	); */

	interface MySubmitForm {
		chartMorning : object
		chartNight : object
		record : {
			level1 : number
			level2 : number
			level3 : number
			stateNormal : number
			stateLow : number
			stateAll : number
		}
	}
	const respInfo = ref<MySubmitForm>(
		{
			chartMorning: {},
			chartNight: {},
			record: {
				level1: 0,
				level2: 0,
				level3: 0,
				stateNormal: 0,
				stateLow: 0,
				stateAll: 0
			}
		})

	//早上和晚上共用opt
	const chartsOpts = ref<any>({
		color: ['#19c3ba', '#93c978'],
		yAxis: {
			data: [{ min: 0, max: 240 }],
			splitNumber: 6
		},
		padding: [10, 10, 0, 6],
		legend: { show: true },
		extra: {
			area: {
				type: "curve",
				opacity: 0.2,
				addLine: true,
				width: 2,
				gradient: true,
				activeType: "hollow"
			}
		},
	});

	const morningChartData = ref();
	const nightChartData = ref();

	const updateMornig = () => {
		console.log("updateMornig: ");
		morningChartData.value = {
			"categories": ["12-06", "12-07", "12-08", "12:09", "12-10", "12-11"],
			"series": [{
				"name": "高压（收缩压）",
				"data": [124, 126, 131, 125, 130, 130]
			}, {
				"name": "低压（舒张压）",
				"data": [81, 76, 80, 74, 76, 78]
			}]
		};
	}

	const updateNight = () => {
		console.log("updateNight: ");
		nightChartData.value = {
			"categories": ["12-06", "12-07", "12-08", "12:09", "12-10", "12-11"],
			"series": [{
				"name": "高压（收缩压）",
				"data": [125, 127, 131, 125, 131, 130]
			}, {
				"name": "低压（舒张压）",
				"data": [81, 79, 80, 74, 81, 79]
			}]
		};
	}


	const gotoManualEntry = (index :number) => {
		// 血压=0； 体温=1； 血糖=2；体脂=3
		console.log("gotoManualEntry: ",index);
		uni.navigateTo({
			url: "/pageHealthMeasure/manualEntry" + "?params=" + index
		})
	}

	const gotoMeasure = () => {
		console.log("gotoMeasure: ");

	}

	const gotoPlan = () => {
		console.log("gotoPlan: ");
		uni.navigateTo({
			url: '/pageChronicDisease/bloodPressureTestPlan'
		})
	}

	/* watch(
		() => props.viewMode,
		(value) => {
			console.log('props.viewMode', props.viewMode);

		},
		{ deep: false, immediate: true }
	); */

	onMounted(() => {
		// 组件能被调用必须是组件的节点已经被渲染到页面上
		setTimeout(() => {
			updateMornig();
			updateNight();
		}, 300);
	})
</script>

<style lang="scss">
	.u-page__tag-item {
		margin-right: 20px;
	}

	.charts-box {
		width: 100%;
		height: 500rpx;
	}
</style>