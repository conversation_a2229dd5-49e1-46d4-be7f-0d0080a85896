<template>
	<view class="px-[20rpx] py-[28rpx]">
		<view class="flex flex-row items-center bg-white rounded-[16rpx] mt-[8rpx] py-[30rpx] px-[40rpx]"
			@click="onClickItem({name:'血压管理',signType:1})">
			<!-- <image src="@/static/products/blood-pressure02.png" class="w-[80rpx] h-[80rpx] object-contain" /> -->
			<uni-icons custom-prefix="iconfont" type="icon-xieya" size="40" color="#014777"></uni-icons>

			<view class="flex flex-col flex-1 pl-[20rpx]">
				<text class="text-lg">血压管理</text>
			</view>

			<view class="flex flex-row items-center">
				<uni-icons type="right" size="32" color="#014777"></uni-icons>
			</view>
		</view>
		
		<view class="flex flex-row items-center bg-white rounded-[16rpx] mt-[28rpx] py-[30rpx] px-[40rpx]"
			@click="onClickItem({name:'血氧管理',signType:2})">
			<!-- <image src="@/static/products/blood-pressure02.png" class="w-[80rpx] h-[80rpx] object-contain" /> -->
			<uni-icons custom-prefix="iconfont" type="icon-xieya" size="40" color="#014777"></uni-icons>
		
			<view class="flex flex-col flex-1 pl-[20rpx]">
				<text class="text-lg">血氧管理</text>
			</view>
		
			<view class="flex flex-row items-center">
				<uni-icons type="right" size="32" color="#014777"></uni-icons>
			</view>
		</view>
		<view class="flex flex-row items-center bg-white rounded-[16rpx] mt-[28rpx] py-[30rpx] px-[40rpx]"
			@click="onClickItem({name:'体温管理',signType:3})">
			<!-- <image src="@/static/products/blood-pressure02.png" class="w-[80rpx] h-[80rpx] object-contain" /> -->
			<uni-icons custom-prefix="iconfont" type="icon-tiwen" size="40" color="#014777"></uni-icons>

			<view class="flex flex-col flex-1 pl-[20rpx]">
				<text class="text-lg">体温管理</text>
			</view>

			<view class="flex flex-row items-center">
				<uni-icons type="right" size="32" color="#014777"></uni-icons>
			</view>
		</view>
		<view class="flex flex-row items-center bg-white rounded-[16rpx] mt-[28rpx] py-[30rpx] px-[40rpx]"
			@click="onClickItem({name:'血糖管理',signType:4})">
			<!-- <image src="@/static/products/blood-pressure02.png" class="w-[80rpx] h-[80rpx] object-contain" /> -->
			<uni-icons custom-prefix="iconfont" type="icon-xietang" size="40" color="#014777"></uni-icons>

			<view class="flex flex-col flex-1 pl-[20rpx]">
				<text class="text-lg">血糖管理</text>
			</view>

			<view class="flex flex-row items-center">
				<uni-icons type="right" size="32" color="#014777"></uni-icons>
			</view>
		</view>
		<view class="flex flex-row items-center bg-white rounded-[16rpx] mt-[28rpx] py-[30rpx] px-[40rpx]"
			@click="onClickItem({name:'体脂管理',signType:5})">
			<!-- <image src="@/static/products/blood-pressure02.png" class="w-[80rpx] h-[80rpx] object-contain" /> -->
			<uni-icons custom-prefix="iconfont" type="icon-tizhi" size="40" color="#014777"></uni-icons>

			<view class="flex flex-col flex-1 pl-[20rpx]">
				<text class="text-lg">体脂管理</text>
			</view>

			<view class="flex flex-row items-center">
				<uni-icons type="right" size="32" color="#014777"></uni-icons>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	
	function onClickItem(obj : any) {
		console.log("obj: " + JSON.stringify(obj));
		uni.$emit('selectSignType', obj);
		uni.navigateBack();
	};
</script>

<style>
</style>