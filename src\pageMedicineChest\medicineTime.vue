<template>
    <view class="page">
        <view class="m-[30rpx]">
            <view class="w-full rounded-[25rpx] bg-[#ffffff]">
                <view class='relative list-border'>
                    <view class="flex flex-row justify-between items-center h-[120rpx] px-[20rpx] [border-bottom:2rpx_solid_#eee]" v-for="(item, index) in timesOptions"
                        :key="item.value">
                        <view class="flex items-center">
                            <uni-icons type="minus-filled" :size='28' color='#FE4D50' @click="deleItem(index)"></uni-icons>
                            <view class="ml-30rpx text-lg">{{item.name}}</view>
                        </view>
                        <!-- <view>
                            <uni-icons type="forward" :size='23'></uni-icons>
                        </view> -->
                    </view>
                </view>
                <view class="flex flex-row items-center h-[120rpx] px-[20rpx] text-[#237FC2]" @click="addItem">
                    <uni-icons class="relative top-2rpx" type="plus-filled" :size='22' color=''></uni-icons>
                    <view class="ml-30rpx">添加时间</view>
                </view>
            </view>

            <up-datetime-picker :show="showPicker" v-model="pickTime" mode="time" @confirm="onConfirm"></up-datetime-picker>

            <view>
                <button @click="confirm()" size="default" class="text-[#fff] bg-[#139d7a] rounded-[50rpx] mt-[50rpx]">
                    确定
                </button>
            </view>
        </view>
    </view>
</template>

<script setup lang="ts">
import { ref } from "vue";

interface radioOptionsItem {
    // value: string;
    name: string;
    checked: boolean;
}

const timesOptions = ref<radioOptionsItem[]>([
    {
        // value: "01",
        name: "07:00",
        checked: true,
    },
    {
        // value: "02",
        name: "12:30",
        checked: true,
    },
    {
        // value: "03",
        name: "18:00",
        checked: true,
    },
]);

const showPicker = ref(false);
const pickTime = ref("");

const deleItem = (index: number) => {
    console.log(index);
    timesOptions.value.splice(index, 1);
};
const addItem = () => {
    showPicker.value = true;
    let len = timesOptions.value.length;
    pickTime.value = timesOptions.value[len - 1]?.name;
};
const onConfirm = () => {
    let findRepeat = timesOptions.value.find(item => item.name === pickTime.value);
    if(findRepeat) {
        uni.showToast({
            title: '时间重复',
            icon: 'none'
        })
        return
    }
    let tempItem = {
        name: pickTime.value,
        checked: true,
    }
    timesOptions.value.push(tempItem);
    showPicker.value = false;
};
const confirm = () => {
    const timeNames = timesOptions.value.map((item) => item.name);
    console.log("当前的name字段：", timeNames);
    uni.$emit("medicineTimeEvent", timeNames.join(","));
    uni.navigateBack({});
};
</script>

<style scoped lang="scss">
.list-border:after {
    position: absolute;
    bottom: 0;
    right: 0;
    left: 0;
    height: 1px;
    content: "";
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
    background-color: #bbb;
}
.list-border:after {
    position: absolute;
    bottom: 0;
    right: 0;
    left: 0;
    height: 1px;
    content: "";
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
    background-color: #bbb;
}

.picker-view {
    width: 650rpx;
    height: 500rpx;
    margin-top: 20rpx;
}

.item {
    line-height: 100rpx;
    text-align: center;
}
</style>