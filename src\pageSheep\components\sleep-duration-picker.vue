<template>
	<view v-if="props.show" style="width: 100%;">
		<!-- 弹窗蒙版 -->
		<view class="mask" catchtouchmove="preventTouchMove"></view>

		<view class="other_popup" :style="'bottom:' + (props.show ? '0px' : '')">

			<view class="flex items-center px-[28rpx] py-[36rpx]">
				<view class="" @click="onCancel">取消</view>
				<view class="flex flex-row flex-1 justify-center px-[36rpx] text-lg font-bold">
					请选择
				</view>

				<view class="text-blue" @click="onConfirm">确定</view>
			</view>

			<picker-view class="picker-view" indicator-style="height: 50px;" :value="pickerIndexes" @change="onChange">
				<picker-view-column>
					<view style="line-height: 50px;" :style="{textAlign: 'center'}" v-for="(item, index) in hourOptions"
						:key="index">
						{{ item }}
					</view>
				</picker-view-column>

				<picker-view-column>
					<view style="line-height: 50px; text-align: center;" v-for="(item2, index) in minOptions" :key="index">
						{{ item2 }}
					</view>
				</picker-view-column>

			</picker-view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { onLoad, onShow, onReady } from "@dcloudio/uni-app";
	import { nextTick } from "vue";
	import { ref, reactive, computed, watch } from 'vue';

	const emits = defineEmits(["onCancel", "onConfirm"]);
	const props = withDefaults(
		defineProps<{
			show : boolean;

		}>(),
		{
			//没有默认值就是必填项
			show: false,

		}
	);

	//// 响应式变量
	const hourOptions = ref<string[]>([
		"0小时",
		"1小时",
		"2小时",
		"3小时",
		"4小时",
		"5小时",
		"6小时",
		"7小时",
		"8小时",
		"9小时",
		"10小时",
		"11小时",
		"12小时",
		"13小时",
		"14小时",
		"15小时",
		"16小时",
		"17小时",
		"18小时",
		"19小时",
		"20小时",
		"21小时",
		"22小时",
		"23小时"
	]);
	const minOptions = ref<string[]>([
		"0分钟",
		"5分钟",
		"10分钟",
		"15分钟",
		"20分钟",
		"25分钟",
		"30分钟",
		"35分钟",
		"40分钟",
		"45分钟",
		"50分钟",
		"55分钟",
	]);

	const pickerIndexes = ref<number[]>([8,0]);

	// picker-view改变时的处理函数
	const onChange = (e) => {
		console.log(e);
		//值是数据列的索引
		pickerIndexes.value = e.detail.value;
	};

	const onConfirm = () => {
		if (!pickerIndexes.value || pickerIndexes.value.length == 0) {
			uni.showToast({
				title: "请选择有效的数值",
				icon: "none"
			})
			return
		}

		let result : string = "";
		let integerVal = hourOptions.value[pickerIndexes.value[0]];
		let decimalVal = minOptions.value[pickerIndexes.value[1]];

		result = integerVal + decimalVal;
		console.log("result", result);

		emits("onConfirm", result)

	};
	const onCancel = (e) => {
		console.log(e);
		emits("onCancel")
	};
</script>
<style scoped>
	.mask {
		width: 100%;
		height: 100vh;
		position: fixed;
		top: 0;
		left: 0;
		background: #000;
		z-index: 900;
		opacity: 0.3;
	}

	.other_popup {
		width: 100%;
		background-color: #fff;
		border-radius: 10upx 10upx 0 0;
		position: fixed;
		left: 0;
		bottom: -1000upx;
		z-index: 999;
		transition: all 0.3s;
	}

	.picker-view {
		width: 750rpx;
		height: 600rpx;
		margin-top: 20rpx;
	}

	.picker-view-column {
		align-items: center;

	}

	.item {
		line-height: 100rpx;
		text-align: center;
	}
</style>