import request from '@/common/request'

/**
 * 创建睡眠评估
 * @param data 请求体参数
 */
export const createSleepAssessment = (data : any) => {
	return request({
		method: 'POST',
		url: '/monitor/need/healthArchive/createSleepAssessment',
		body: data,
		hideLoading: false,
		/* custom: {
			ignoreRespInterceptors: true //忽略本次请求的响应拦截
		} */
	})
}
 
 
 /**
  * 查询家庭成员的睡眠评估
  * @param data 请求体参数
  */
 export const queryPersonSleepAssessment = (personId : any) => {
 	return request({
 		method: 'POST',
 		url: '/monitor/need/healthArchive/queryPersonSleepAssessment?personId=' + personId,
 		body: null,
 		hideLoading: false,
 		/* custom: {
 			ignoreRespInterceptors: true //忽略本次请求的响应拦截
 		} */
 	})
 }
  