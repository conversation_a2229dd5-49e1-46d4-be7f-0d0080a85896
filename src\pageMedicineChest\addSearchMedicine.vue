<template>
	<view class="container w-full h-full">
		<view class="flex-col">
			<z-paging ref="paging" :refresher-enabled="false" :loading-more-enabled="false" :safe-area-inset-bottom="true">
				<!-- 之后-vue3 -->
				<template #top>
					<view class="item-01">
						<u-search placeholder="搜索药物" bgColor="#DCEDFF" v-model="searchValue" :focus="true" :showAction="false"
							class="search" @change="reptileList"></u-search>
					</view>
				</template>

				<scroll-view scroll-y="true" class="container-list">
					<view class="list-item" v-for="item in list" :key="item.id" @click="onClickItem(item)">
						<text class="name">{{ item.name }}</text>
						<text class="uni-icon iconfont icon-xiangxia"></text>
					</view>
					<view class="tip" v-if="!list.length">无搜索内容</view>
					<view style="height: 120rpx;"></view>
				</scroll-view>

			</z-paging>
		</view>

		<!-- popup组件内部使用了u-popup,造成popup渲染有问题,如果需要弹窗，需要先修复 -->
		<!-- <popup :visible.sync="visible" :title="selectDrug.name">
			<view class="c-body">
				<scroll-view scroll-y="true" style="height: 100%;">
					<view v-for="(item, index) in selectDrug.attrList" :key="index" class="info-item">
						<view class="title">{{ item.attrKey }}</view>
						<view class="value" v-html="item.attrValue"></view>
					</view>
				</scroll-view>
			</view>
			<view slot="footer" class="c-footer flex">
				<view class="xx-button" @click="onClickAdd">选择药物</view>
			</view>
		</popup> -->
	</view>
</template>

<script setup lang="ts">
	import { ref } from 'vue';
	import { onLoad, onShow, onUnload } from "@dcloudio/uni-app";
	import { reptileList } from '@/common/api/medicine';

	const searchValue = ref('');
	const list = ref<any[]>([]);
	const visible = ref(false);
	let selectDrug : any = {};

	function getList() {
		const params = {
			condition: {
				name: searchValue.value
			},
			pageNum: 1,
			pageSize: 40,
			sort: ""
		}
		reptileList(params).then((res : any) => {
			let data = res.data || {}
			list.value = data.list || []
		})
	};
	function onClickItem(obj : any) {
		console.log("obj: " + JSON.stringify(obj));
		selectDrug = obj;
		uni.$emit('selectDrug', selectDrug);
		uni.navigateBack();
		// console.log(obj)
		// /visible.value = true
	};

	function onClickAdd() {
		uni.$emit('selectDrug', selectDrug);
		uni.navigateBack();
	};

	onLoad(() => {
		getList();
	});

	onShow(() => {
		console.log("HISTORY_INDEX【onShow】：页面重新可见");

		//检查token情况 
	});

	onUnload(() => {

		console.error('页面onUnload');
	})
</script>

<style lang="scss" scoped>
	.container {
		display: flex;
		flex-direction: column;
		background: #F7F9FC;

		.item-01 {
			margin: 24rpx;

			::v-deep .u-search__content {
				height: 64rpx;
			}
		}

		.container-list {
			position: relative;
			width: 100%;
			flex: 1;
			box-sizing: border-box;
			font-size: 28rpx;
			overflow: hidden;
			background: white;
		}

		.tip {
			text-align: center;
			padding-top: 40px;
			color: #999;
		}
	}

	.list-item {
		display: flex;
		justify-content: space-between;
		height: 100rpx;
		line-height: 100rpx;
		border-bottom: 1px solid #f3f3f3;
		padding: 0 32rpx;

		.uni-icon {
			color: #999;
			transform: rotate(-90deg);
		}
	}

	.c-body {
		height: 60vh;
		overflow: hidden;

		.info-item {
			margin-bottom: 40rpx;

			.title {
				font-size: 30rpx;
				font-weight: bold;
			}

			.value {
				padding-top: 8rpx;
			}
		}
	}
</style>