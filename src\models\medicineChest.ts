/*
 * @Description:
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-11-29 
 * @LastEditTime: 2024-11-29
 * @LastEditors: huangyanqiu
 */
// 药箱-当天计划与记录

interface MedicineItem {
	name : string;      // 药品名称（如"阿莫西林"）
	type : string;      // 药品类型（如"药片"）
	quantity : number;  // 药品数量（如1片）
}

interface MedicineRecord {
	time : string;                   // 用药时间（如"07:00"）
	medicines : MedicineItem[];       // 药品列表
	status : "计划中" | "已服用";     // 计划/记录状态
}

export interface MedicinePlanAndRecord {
	date : string;                   // 日期（如"2024-11-29"）
	plan : MedicineRecord[];         // 用药计划（"计划中"状态）
	records : MedicineRecord[];      // 用药记录（"已服用"状态）
}
/* 我的药箱 */
export interface MedicineBoxItem {
	id : string;
	name : string;                // 药品名称
	type : string;                // 药品类型（如 "胶囊"）
	description : string;         // 药品描述
	status ?: string;  // 状态（可选，"计划中" | "已结束" 如果不返回则根据时间计算）
	days ?: number;               // 用药天数（可选，如果没有返回状态时需要返回）
	startDate ?: string;
	checked ?: boolean;// 开始用药时间（可选，如果没有返回状态时需要返回）
	[keys : string] : any
}
 