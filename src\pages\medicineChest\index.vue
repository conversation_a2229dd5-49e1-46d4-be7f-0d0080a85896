<template>
	<view>
		<!-- 计划与记录 -->
		<view class="bg-white p-[30rpx]">
			<!-- 日期 -->
			<view class="flex justify-center items-center h-[96rpx]">
				<text>今日</text><text class="text-2xl text-[#014777]">&nbsp;2024-11-28&nbsp;</text><uni-icons type="calendar"
					size="26" color="#333"></uni-icons>
			</view>
			<!-- 计划列表 -->
			<view class="py-2 text-lg">今日计划</view>
			<view>
				<view v-if="medicineData.plan.length === 0" class="bg-[#ecfafb] px-[30rpx] py-3 mt-[15rpx]">

					<view class="flex items-center pt-[10rpx]">
						<uni-icons custom-prefix="iconfont" type="icon-zanwushuju" size="62" color="#999"></uni-icons>
						<text class="pl-[20rpx] text-[#333]">暂无计划</text>
					</view>
				</view>
				<!-- 如果 medicineData.plan 有数据，渲染以下内容 -->
				<view v-else>
					<view class="bg-[#ecfafb] px-[30rpx] py-3 mt-[15rpx]" v-for="(planItem, index) in medicineData.plan"
						:key="index">
						<view class="flex justify-between">
							<text>{{ planItem.time }}</text>
						</view>
						<view class="flex items-center pt-[10rpx]" v-for="(medicine, index) in planItem.medicines" :key="index">
							<image :src="`/static/medecine-type/${getImageNameByType(medicine.medicineType)}-type.png`"
								class="w-[80rpx] h-[80rpx] object-contain bg-[#0DBFC6] rounded-lg" />
							<text class="relative flex-1 pl-[20rpx] h-[80rpx] list-border">{{ medicine.name }}</text>
						</view>
					</view>
				</view>
			</view>
			<!-- 记录列表 -->
			<view class="py-2 text-lg">用药记录</view>
			<view>
				<view v-if="medicineData.records.length === 0" class="bg-[#F8F8F8] px-[30rpx] py-3 mt-[15rpx]">

					<view class="flex items-center pt-[10rpx]">
						<uni-icons custom-prefix="iconfont" type="icon-zanwushuju" size="62" color="#999"></uni-icons>
						<text class="pl-[20rpx] text-[#333]">暂无数据</text>
					</view>
				</view>
				<!-- 如果 medicineData.records 有数据，渲染以下内容 -->
				<view v-else>
					<view class="bg-[#F8F8F8] px-[30rpx] py-3 mt-[15rpx]" v-for="(recordsItem, index) in medicineData.records"
						:key="index">
						<view class="flex justify-between">
							<text>{{ recordsItem.time }}</text>
						</view>
						<view class="flex items-center pt-[10rpx]" v-for="(medicine, index) in recordsItem.medicines" :key="index">
							<image :src="`/static/medecine-type/${getImageName(medicine.type)}-type.png`"
								class="w-[80rpx] h-[80rpx] object-contain bg-[#0DBFC6] rounded-lg" />
							<text class="relative flex-1 pl-[20rpx] h-[80rpx] list-border">{{ medicine.name }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 我的药箱 -->
		<view>
			<view class="m-[30rpx]">
				<text class="text-2xl">我的药箱</text>
			</view>
			<!-- tabs -->
			<view class="m-[30rpx] flex justify-center">
				<u-tabs :list="boxname" :activeStyle="{
			    color: '#014777',
				fontSize:'46rpx',
			    transform: 'scale(1.05)'
			}" :inactiveStyle="{
			    color: '#333',
				fontSize:'36rpx',
			    transform: 'scale(1)'
			}" @click="click"></u-tabs>
			</view>
			<!-- tabs内容 -->
			<view v-if="selectedTab === '短期用药'">
				<uni-card>
					<view class="flex flex-row items-center py-[12rpx]" v-for="(medicine, index) in medicineBox"
						:key="index">
						<view>
							<image :src="`/static/medecine-type/${getImageName(medicine.type)}-type.png`"
								class="w-[120rpx] h-[120rpx] object-contain bg-[#01558D] rounded-lg" />
						</view>
						<view class="relative flex flex-row flex-1 justify-between list-border items-center  h-[120rpx]">
							<view class="flex flex-col">
								<text class="flex-1 pl-[20rpx] text-lg text-[#333]">{{ medicine.name }}</text>
								<view class="flex flex-row">
									<text class="flex-1 pl-[20rpx]">{{ medicine.type }}</text>
									<uni-link class="pl-[20rpx]" href="#" text="说明书" color="#01558D"></uni-link>
								</view>
							</view>
							<uni-icons type="right"></uni-icons>
						</view>
					</view>
				</uni-card>
			</view>
			<view v-if="selectedTab === '长期用药'">
				<uni-card>
					<view class="flex items-center p-3">
						<uni-icons custom-prefix="iconfont" type="icon-zanwushuju" size="62" color="#999"></uni-icons>
						<text class="pl-[20rpx] text-[#333]">暂无数据</text>
					</view>
				</uni-card>
			</view>
		</view>
		<view class="grid grid-cols-2 gap-4 p-5">
			<u-button type="primary" :plain="true" class="custom-style" text="管理用药" @click="handleBindBoxMedical"></u-button>
			<u-button type="primary" :plain="true" text="添加用药" @click="handleBindAddMedical"></u-button>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { reactive, ref, onMounted } from "vue";
	import { onLoad, onShow } from "@dcloudio/uni-app";
	import moment from 'moment';
	import type { MedicinePlanAndRecord, MedicineBoxItem } from '@/models/medicineChest'
    import { queryMedicineTask } from '@/common/api/medicine'

	const boxname = reactive([
		{ name: '短期用药' },
		{ name: '长期用药' }
	]);
	// 当前选中的 Tab
	const selectedTab = ref('短期用药');
	const medicineData : MedicinePlanAndRecord = reactive({
		date: moment().format('YYYY-MM-DD'), // 2024-11-29
		plan: [
			{
				time: "07:00",
				medicines: [
					{ name: "阿莫西林", type: "药片", quantity: 1 },
					{ name: "感冒灵", type: "胶囊", quantity: 2 }
				],
				status: "计划中"  // 这是计划中的药品
			},
			{
				time: "12:00",
				medicines: [
					{ name: "阿莫西林", type: "药片", quantity: 1 }
				],
				status: "计划中"  // 这是计划中的药品
			},
			{
				time: "18:00",
				medicines: [
					{ name: "阿莫西林", type: "药片", quantity: 1 },
					{ name: "感冒灵", type: "胶囊", quantity: 2 }
				],
				status: "计划中"  // 这是计划中的药品
			}
		],
		records: [
			{
				time: "07:00",
				medicines: [
					{ name: "阿莫西林", type: "药片", quantity: 1 },
					{ name: "感冒灵", type: "胶囊", quantity: 2 }
				],
				status: "已服用"  // 这是实际服用的药品
			},
			{
				time: "12:00",
				medicines: [
					{ name: "阿莫西林", type: "药片", quantity: 1 }
				],
				status: "已服用"  // 这是实际服用的药品
			}
		]
	});
	const getImageName = (type : any) => {
		switch (type) {
			case '胶囊':
				return 'jiaonang';
			case '药片':
				return 'pian';
			case '软膏':
				return 'ruangao';
			case '液体':
				return 'yiti';
			case '设备':
				return 'shebei';
			default:
				return 'default'; // 如果没有匹配到类型，使用默认图片
		}
	}
    const getImageNameByType = (type : any) => {
		switch (type) {
            case 1:
				return 'jiaonang';
            case 2:
				return 'pian';
            case 3:
				return 'ruangao';
			case 4:
				return 'yiti';
			case 5:
				return 'shebei';
			default:
				return 'default'; // 如果没有匹配到类型，使用默认图片
		}
	}
	const medicineBox : MedicineBoxItem[] = [
		{
			id: '1',
			name: "阿莫西林",
			type: "胶囊",
			description: "用于治疗细菌感染的抗生素",
			status: "已结束",  // 后端返回状态
		},
		{
			id: '2',
			name: "布洛芬",
			type: "液体",
			description: "用于缓解感冒症状",
			status: "计划中",  // 后端返回状态
		}
	];
	const handleBindAddMedical = () => {
		uni.navigateTo({
			url: "/pageMedicineChest/addMedicine",
			animationType: "slide-in-right",
			animationDuration: 300,
		})
	}
	const handleBindBoxMedical = () => {
		uni.navigateTo({
			url: "/pageMedicineChest/boxMedicine",
			animationType: "slide-in-right",
			animationDuration: 300,
		})
	}
	// 定义方法  
	function click(item : any) {
		selectedTab.value = item.name;
		console.log('选中的 Tab:', item.name);
	}
	const customStyle = reactive({
		marginTop: '20px',
		color: 'red'
	});

    const getMedicineTask = () => {
        let oPersonInfo = getCurrentMemberInfo();
        let params = {
            "personId": oPersonInfo?.personId,
            "planTime": moment().format('YYYY-MM-DD')
        }
        queryMedicineTask(params).then((res: any) => {
            if(res.success) {
                console.log('queryMedicineTask=====', res.data);
                let data = res.data || [];
                let oTarget:any = {};
                data.map((item: any) => {
                    let planTime = item.planTime;
                    if(!oTarget[planTime]){
                        oTarget[planTime] = [];
                    }
                    oTarget[planTime].push(item)
                })
                let aTarget:any = [];
                data.map((item: any) => {
                    let planTime = item.planTime;
                    item.name = item.medicineName;
                    let time = item.planTime.split(' ')?.[1];
                    let oTemp = { planTime, time,  medicines: [item] }
                    let findItem = aTarget.find((_o:any)=>_o.planTime === planTime);
                    findItem ? findItem.medicines.push(item) :aTarget.push(oTemp);
                })
                medicineData.plan = aTarget;
                console.log(oTarget)
                console.log(aTarget)
            }
        }).catch((err) => {
            console.log(err);
        })
    }

    const getCurrentMemberInfo = () => {
        console.log('历史数据页面加载onShow完成')
		let lastUserInfo = uni.getStorageSync('lastSelectedUserInfo');
		let userInfo = uni.getStorageSync('userInfo');
		if (!lastUserInfo || !lastUserInfo.personId) {
			if (!userInfo || !userInfo.personId) {
				uni.showModal({
					content: "用户获取失败，请重新登录",
					success: (res) => {
						if (res.confirm) {
							uni.reLaunch({
								url: '/pages/login'
							})
						}
					}
				})
				return
			} else {
				return { personId: userInfo.personId, personName: userInfo.personInfo.personName }
			}
		} else {
			return { personId: lastUserInfo.personId, personName: lastUserInfo.personName }
		}
    }
    onMounted(() => {
        // getMedicineTask()
    })

    
	onLoad(() => {
		console.log('MEDICINE_CHEST【onLoad】：页面加载完成')
	})

	onShow(() => {
		console.log("MEDICINE_CHEST【onShow】：页面重新可见");
        getMedicineTask()
	});
</script>

<style scoped lang="scss">
	.list-border:after {
		position: absolute;
		bottom: 0;
		right: 0;
		left: 0;
		height: 1px;
		content: "";
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5);
		background-color: #bbb;
	}

	.custom-style {
		color: #ff0000;
		width: 400rpx;
	}
</style>