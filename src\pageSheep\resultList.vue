<template>
	<view class="p-[30rpx]">
		<view class="bg-white rounded-[20rpx] p-[30rpx] mt-[30rpx] mb-[30rpx]">
			<view class="flex flex-row justify-center items-center">
				<view class="flex flex-col flex-1 justify-center  items-center">
					<view class="text-[#000] p-2 text-lg">5~10<text class="text-[#999]">&nbsp;分钟</text></view>
					<view class="text-[#999]">评测时长</view>
				</view>
				<up-line direction="col"></up-line>
				<view class="flex flex-col flex-1 justify-center  items-center">
					<view class="text-[#000] p-2 text-lg">18<text class="text-[#999]">&nbsp;题</text></view>
					<view class="text-[#999]">评测题数</view>
				</view>
			</view>
			<view class="px-2 py-5">
				<view class="text-base">1.请您仔细阅读每条问题，然后根据您最近一个月 的睡眠情况，如实填写相关信息。</view>
				<view class="text-base">2.测评结束后，将会得到您睡眠质量评分及等级。</view>
			</view>
			<button class="my-2" type="primary" style="background-color: #2B97E5; color: white;"
				@click="onClickTest">开始测评</button>
		</view>
		<text class="text-lg">评测记录</text>
		<view class="bg-white rounded-[20rpx] p-[30rpx] mt-[30rpx] mb-[30rpx]">
			<view class="flex flex-col" v-for="(item, index) in list" :key="index">
				<view class="flex flex-row justify-between items-center">
					<view>
						<view class="text-[#333] text-lg">自评得分<text class="text-[#333] text-lg">&nbsp;{{ item.score}}分</text></view>
						<view class="text-[#999]">{{item.assessTime}}</view>
					</view>
					<view>
						<text class="text-[#E99D42]">{{ item.content }}</text>
					</view>
				</view>
				<up-line v-if="index < (list.length - 1)" color="#333" margin="20px 0"></up-line>
			</view>

		</view>
		<up-text type="primary" text="显示所有数据" align="center" margin="20rpx 0 0 0"></up-text>

		<view class="bg-white rounded-[20rpx] p-[30rpx] mt-[30rpx] mb-[30rpx]">
			<view class="flex flex-row items-center" @click="gotoPittsburgh">
				<uni-icons custom-prefix="iconfont" type="icon-shuimian1" size="42" color="#19c3ba"></uni-icons>
				<text class="text-lg text-[#333] pl-5">了解匹兹堡睡眠质量指数</text>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { ref, onMounted } from 'vue';
	import { onLoad, onShow, onUnload } from "@dcloudio/uni-app";
	import { queryPersonSleepAssessment } from '@/common/api/sleep';

	const selectMenberType =ref<{personName:string,personId:string|number}>();
	let userInfo : any = null;
	const list = ref<any[]>([]);
	const onClickTest = () => {
		console.log('进入评测')
		uni.navigateTo({
			url: "/pageSheep/sheepTest"
		})
	}

	const gotoPittsburgh = () => { 
		uni.navigateTo({
			url: "/pageSheep/pittsburghSleepWiki"
		})
	}
	
	const requestRecord = () => { 
		queryPersonSleepAssessment(userInfo.personId).then((res) => {
			console.log('createSleepAssessment********', res);

			list.value = res.data;
		}).catch((error) => {
			console.log('requestDataWithDate!!!');
			uni.showToast({
				title: '数据请求失败',
				icon: 'none'
			})
		})
	};
    // const isRefresh = ref(false)
    onShow(() =>{
        let pages = getCurrentPages();
        let currPage = pages[pages.length - 1];
        console.log(currPage)
        if(currPage.__data__.isRefresh) {
            // 重新获取数据
            requestRecord()//获取列表数据
            // 每一次需要清除，否则会参数会缓存
            currPage.__data__.isRefresh = false
        }
    })

	onMounted(() => {
		let lastUserInfo = uni.getStorageSync("lastSelectedUserInfo");
		userInfo = uni.getStorageSync("userInfo");
		
		if (!lastUserInfo || !lastUserInfo.personId) {
			if (!userInfo || !userInfo.personId) {
				uni.showModal({
					content: "用户信息获取失败，请重新登录",
					success: (res) => {
						if (res.confirm) {
							uni.reLaunch({
								url: '/pages/login',
							})
						}
					}
				})
				return
			}else{
				selectMenberType.value = {personId:userInfo.personId, personName:userInfo.personInfo.personName}
			}
		}else{
			selectMenberType.value = {personId:lastUserInfo.personId, personName:lastUserInfo.personName}
		} 
		
		requestRecord();

	});
</script>

<style scoped lang="scss">
</style>