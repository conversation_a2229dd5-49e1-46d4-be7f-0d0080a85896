<template>
	<view class="flex-col">
		<z-paging ref="paging" :refresher-enabled="false" :loading-more-enabled="false" :safe-area-inset-bottom="true">
			<!-- 之后-vue3 -->
			<template #top>
				<view class="flex flex-col justify-center m-[30rpx]">
					<view class=" flex flex-row justify-between items-center rounded-[20rpx] bg-[#ffffff] p-[30rpx]">
						<view class="flex flex-row items-center"> 
							<view class="text-[36rpx]">{{ selectMenberType.personName || "" }}</view>
						</view>
						<view class="flex flex-row items-center" @click='selectedMenber'>
							<uni-icons type="list" size="23" color="#014777"></uni-icons>
							<view class="text-[36rpx] text-[#333]">切换家庭成员</view>
						</view>
					</view>

					<view class="flex justify-center mt-[20rpx]">
						<u-tabs :list="types" :activeStyle="{
					    color: '#014777',
							fontSize:'46rpx',
					    transform: 'scale(1.05)'
					}" :inactiveStyle="{
					    color: '#333',
					fontSize:'36rpx',
					    transform: 'scale(1)'
					}" @click="tabItemClick"></u-tabs>
					</view>
				</view>

			</template>
			
			<!-- 要使用v-if，，v-show会造成图表不显示 -->
			<bloodPressure v-if="curTabIndex === 0" class="mt-[20rpx]" />
			<bloodSugar v-if="curTabIndex === 1" class="mt-[20rpx]" />
			<bmi v-if="curTabIndex === 2" class="mt-[20rpx]" />
		</z-paging>
	</view>
</template>

<script setup lang="ts">
	import { onMounted, ref, reactive } from "vue";
	import { onLoad, onShow, onUnload } from "@dcloudio/uni-app";

	import type { IHealthObsResp } from '@/models/healthMeasure'
	import bloodPressure from "./components/bloodPressure.vue";
	import bloodSugar from "./components/bloodSugar.vue";
	import bmi from "./components/bmi.vue";

	const params = ref<IHealthObsResp>();
	const firstLoadType = ref<string>();

	const selectMenberType = ref<{ personName : string, personId : string | number }>();
	const types = reactive([
		{ name: '血压数据' },
		{ name: '血糖数据' },
		{ name: '体脂数据' }
	]);
	const curTabIndex = ref<number>(0);
	const viewMode = ref<string>('day');

	// 定义方法  
	function tabItemClick(item : any) {
		console.log('item', item);
		curTabIndex.value = item.index;
	}
	
	const selectedMenber = () => {
		uni.navigateTo({
			url: '/pageFamily/familyMenber',
			animationType: "slide-in-right",
			animationDuration: 300,
	
		})
	}

	onLoad((option : any) => {
		//{"type":"1"}
		console.log('HISTORY_INDEX【onLoad】：页面加载完成', JSON.stringify(option));
		firstLoadType.value = option.params;
		console.log('firstLoadType', firstLoadType.value);

		uni.$on("selectMenberType", (event) => {
			if (!event) {
				return
			}
			console.log('selectMenberType CALLBACK', event);

			uni.setStorageSync("lastSelectedUserInfo", event);
			selectMenberType.value = event; 
			
			//调接口
		});
		
		let lastUserInfo = uni.getStorageSync("lastSelectedUserInfo");
		let userInfo = uni.getStorageSync("userInfo");
		
		console.log("lastUserInfo", lastUserInfo);
		console.log("userInfo", userInfo);
		if (!lastUserInfo || !lastUserInfo.personId) {
			if (!userInfo || !userInfo.personId) {
				uni.showModal({
					content: "用户信息获取失败，请重新登录",
					success: (res) => {
						if (res.confirm) {
							uni.reLaunch({
								url: '/pages/login',
							})
						}
					}
				})
				return
			} else { 
				selectMenberType.value = { personId: userInfo.personId, personName: userInfo.personInfo.personName }
			console.log("？？222222？？？？",selectMenberType.value);
			}
		} else {
			
			console.log("？？？？？？");
			selectMenberType.value = { personId: lastUserInfo.personId, personName: lastUserInfo.personName }
		}
	})

	onShow(() => {
		console.log("HISTORY_INDEX【onShow】：页面重新可见");


	});
	
	onUnload(() => {
		console.error('页面onUnload');
		uni.$off("selectSignType");
		uni.$off("selectMenberType");
	})
</script>

<style scoped lang="scss">

</style>