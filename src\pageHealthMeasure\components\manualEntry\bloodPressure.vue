<template>
	<view class="flex flex-col items-stretch m-[28rpx] px-[20rpx] py-[28rpx]">

		<up-form ref="uFormRef" labelPosition="left" :rules="rules" :model="submitForm">
			<view class="flex items-center bg-[#ffffff] rounded-[16rpx] px-[20rpx] py-[36rpx]">
				<view class="text-lg">高压</view>
				<view class="flex-1 pl-[36rpx]">
					<up-form-item label="" prop="high" labelWidth="0rpx">
						<view style="width: 100%;" @click="showHighPicker=true">
							<up-input v-model="submitForm.high" placeholder="输入高压值" border="none" readonly />
						</view>
						<up-picker :show="showHighPicker" :columns="highOptions" @cancel="showHighPicker=false"
							:defaultIndex="[highDefIndex]" @confirm="changeConfirmHigh"></up-picker>
					</up-form-item>
				</view>

				<view class="flex flex-row items-center">
					<view class="px-[12rpx] text-[#999]">mmHg</view>
					<uni-icons type="right" size="32" color="#014777"></uni-icons>
				</view>
			</view>

			<view class="flex items-center bg-[#ffffff] rounded-[16rpx] mt-[36rpx] px-[20rpx] py-[36rpx]">
				<view class="text-lg">低压</view>
				<view class="flex-1 pl-[36rpx]">
					<up-form-item label="" prop="low" labelWidth="0rpx">
						<view style="width: 100%;" @click="showLowPicker=true">
							<up-input v-model="submitForm.low" placeholder="输入低压值" border="none" readonly />
						</view>
						<up-picker :show="showLowPicker" :columns="lowOptions" @cancel="showLowPicker=false" :defaultIndex="[lowDefIndex]"
							@confirm="changeConfirmLow"></up-picker>
					</up-form-item>
				</view>

				<view class="flex flex-row items-center">
					<view class="px-[12rpx] text-[#999]">mmHg</view>
					<uni-icons type="right" size="32" color="#014777"></uni-icons>
				</view>
			</view>

			<view class="flex items-center bg-[#ffffff] rounded-[16rpx] mt-[36rpx] px-[20rpx] py-[36rpx]">
				<view class="text-lg">脉搏</view>
				<view class="flex-1 pl-[36rpx]">
					<up-form-item label="" prop="pulse" labelWidth="0rpx">
						<view style="width: 100%;" @click="showPulsePicker=true">
							<up-input v-model="submitForm.pulse" placeholder="输入脉搏" border="none" readonly />
						</view>
						<up-picker :show="showPulsePicker" :columns="pulseOptions" @cancel="showPulsePicker=false"
							:defaultIndex="[pulseDefIndex]" @confirm="changeConfirmPulse"></up-picker>
					</up-form-item>
				</view>

				<view class="flex flex-row items-center">
					<view class="px-[12rpx] text-[#999]">bpm</view>
					<uni-icons type="right" size="32" color="#014777"></uni-icons>
				</view>
			</view>

			<view class="flex items-center bg-[#ffffff] rounded-[16rpx] mt-[36rpx] px-[20rpx] py-[36rpx]">
				<view class="text-lg">时间</view>
				<view class="flex-1 pl-[36rpx]">
					<up-form-item label="" prop="datetime" labelWidth="0rpx">
						<uni-datetime-picker type="datetime" v-model="submitForm.datetime" />
					</up-form-item>
				</view>

				<uni-icons type="right" size="32" color="#014777"></uni-icons>
			</view>

		</up-form>

		<view class="pt-[36rpx] pb-[12rpx] font-bold">测量时活动</view>
		<view class="flex flex-col bg-[#ffffff] rounded-[16rpx] mt-[8rpx] px-[20rpx] py-[36rpx]">
			<up-grid :border="false" col="4" gap="12rpx" align="center">
				<up-grid-item v-for="(item,index) in actionList" :key="index">
					<!-- <uni-tag class="my-[12rpx]" style="min-width: 48rpx;" :text="item.text" :disabled="item.disable"
						:inverted="!item.checked" type="primary" @click="radioClick(index)" /> -->
					<up-tag class="my-[12rpx]" style="min-width: 48rpx;" :text="item.text" shape="circle"
						:bgColor="item.checked ? '#0081ff' : '#ccc'" :borderColor="item.checked ? '#0081ff' : '#ccc'"
						@click="radioClick(index)"> </up-tag>
				</up-grid-item>
			</up-grid>
		</view>

		<view class="flex flex-1 justify-center text-white bg-[#0052A8] rounded-[16rpx] mt-[50rpx] px-[20rpx] py-[36rpx]"
			@click="doSubmit()">
			确定
		</view>
	</view>
</template>

<script setup lang="ts">
	import moment from 'moment';
	import { onLoad, onShow } from "@dcloudio/uni-app";
	import { onMounted, ref, reactive, computed, watch } from 'vue';
	import type { UniFormRef } from '@/uni_modules/uview-plus/types'
	const uFormRef = ref<UniFormRef | null>(null)
	import { createBpRecord } from '@/common/api/measure';
	import { getBloodPressureOptions, getPulseValueOptions } from '@/model_static/bloodPresure'
	import type { IHealthObsResp } from '@/models/healthMeasure'
	import type { CommonTag } from '@/models/common'
	import {
		getMonthTagList,
		fillKeyNameByTypeData
	} from "@/model_static/history";

	import {
		getDateMeasureRecord,
		getMonthMeasureRecord
	} from '@/common/api/measure'

	const selectMenberType = ref<{ personName : string, personId : string | number }>();

	const props = withDefaults(
		defineProps<{
			viewMode : string;
			dataType : string;
		}>(),
		{
			//没有默认值就是必填项
		}
	);

	interface MySubmitForm {
		high : string
		low : string
		pulse : string
		PulseRate : string
		datetime : string
		action : string
	}
	const submitForm = ref<MySubmitForm>(
		{
			high: "135",
			low: "85",
			pulse: "80",
			PulseRate: "",
			datetime: "",
			action: "无"
		})

	const showHighPicker = ref(false);
	const highOptions = reactive([getBloodPressureOptions()]);
	const showLowPicker = ref(false);
	const lowOptions = reactive([getBloodPressureOptions()]);
	const showPulsePicker = ref(false);
	const pulseOptions = reactive([getPulseValueOptions()]);

console.log('lowDefIndex', 85 - Number(lowOptions[0][0]));
console.log('highDefIndex', 135 - Number(highOptions[0][0]));
console.log('pulseDefIndex', 80 - Number(pulseOptions[0][0]));
	const lowDefIndex = ref(85 - Number(lowOptions[0][0]));
	const highDefIndex = ref(135 - Number(highOptions[0][0]));
	const pulseDefIndex = ref(80 - Number(pulseOptions[0][0]));
	const changeConfirmHigh = (e) => {
		console.log('change', e);
		submitForm.value.high = String(e.value[0]);
		showHighPicker.value = false;
		highDefIndex.value = e.indexs[0];
	};

	const changeConfirmLow = (e) => {
		console.log('change', e);
		submitForm.value.low = String(e.value[0]);
		showLowPicker.value = false;
		lowDefIndex.value = e.indexs[0];
	};
	const changeConfirmPulse = (e) => {
		console.log('change', e);
		submitForm.value.pulse = String(e.value[0]);
		showPulsePicker.value = false;
		pulseDefIndex.value = e.indexs[0];
	};

	const actionList = ref<CommonTag[]>(
		[
			{
				text: '无',
				value: 0,
				checked: true,
				disable: false
			}, {
				text: '运动',
				value: 2,
				checked: false,
				disable: false
			}, {
				text: '吃饭',
				value: 3,
				checked: false,
				disable: false
			}, {
				text: '起床',
				value: 4,
				checked: false,
				disable: false
			}, {
				text: '睡前',
				value: 5,
				checked: false,
				disable: false
			}
		]
	);
	const rules = {
		'high': {
			type: 'string',
			required: true,
			message: '请输入高压值',
			trigger: ['blur'],
		},
		'low': {
			type: 'string',
			required: true,
			message: '请输入低压值',
			trigger: ['blur'],
		},
		'pulse': {
			type: 'string',
			required: true,
			message: '请输入脉搏',
			trigger: ['blur'],
		},
		'PulseRate': {
			type: 'string',
			required: true,
			message: '请输入脉率',
			trigger: ['blur'],
		},
		'datetime': {
			type: 'string',
			required: true,
			message: '请选择时间',
			trigger: ['blur', 'change'],
		}
	};
	const radioClick = (myIndex : number) => {
		console.log('radioClick', myIndex);
		submitForm.value.action = actionList.value[myIndex].text;

		actionList.value.forEach((item, index) => {
			item.checked = index === myIndex;
		});
	};

	const doSubmit = () => {
		console.log("submitForm: " + JSON.stringify(submitForm.value));
		if (!uFormRef.value) {
			return
		}

		uFormRef.value.validate().then((valid : boolean) => {

			console.log(valid);
			if (valid) {

				if (!submitForm.value.action) {
					uni.showToast({
						title: "请选择活动",
						icon: "none"
					})
					return
				}

				//请求接口  
				let bodyParams = {
				  "dbp": submitForm.value.low,
				  "measureTimeStr": moment(submitForm.value.datetime).format("YYYY-MM-DD HH:mm"),
				  "others": submitForm.value.action,
				  "personId": selectMenberType.value?.personId,
				  "personName": selectMenberType.value?.personName,
				  "pulse": submitForm.value.pulse,
				  "sbp": submitForm.value.high
				}
			createBpRecord(bodyParams).then((res) => {
			 uni.showToast({
			 	title: '提交成功',
			 	icon: 'none'
			 })
			 
			 submitForm.value = {
			 		high: "",
			 		low: "",
			 		pulse: "",
			 		PulseRate: "",
			 		datetime: "",
			 		action: ""
			 	};
		}).catch((error) => { 
			uni.showToast({
				title: '提交失败',
				icon: 'none'
			})
			 
		})

			} else {
				uni.showToast({
					title: "请规范完成表单录入",
					icon: "none"
				})
			}
		}).catch((error : any) => {

			console.log("error: " + JSON.stringify(error));
			// 处理验证错误  
			uni.showToast({
				title: "请规范完成表单录入",
				icon: "none"
			})
		});
	}

	watch(
		() => props.viewMode,
		(value) => {
			console.log('props.viewMode', props.viewMode);

		},
		{ deep: false, immediate: true }
	);
	
	onMounted(() => {
		let lastUserInfo = uni.getStorageSync("lastSelectedUserInfo");
		let userInfo = uni.getStorageSync("userInfo");
	
		if (!lastUserInfo || !lastUserInfo.personId) {
			if (!userInfo || !userInfo.personId) {
				uni.showModal({
					content: "用户信息获取失败，请重新登录",
					success: (res) => {
						if (res.confirm) {
							uni.reLaunch({
								url: '/pages/login',
							})
						}
					}
				})
				return
			} else {
				selectMenberType.value = { personId: userInfo.personId, personName: userInfo.personInfo.personName }
			}
		} else {
			selectMenberType.value = { personId: lastUserInfo.personId, personName: lastUserInfo.personName }
		} 
	
	});
</script>

<style lang="scss">
	.u-page__tag-item {
		margin-right: 20px;
	}
</style>